/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n      @top-center {\n        content: \"${title}\";\n        font-size: 10pt;\n        font-weight: bold;\n      }\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content { column-count: 2; column-gap: 10mm; }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Fix LaTeX formatting issues\n                    const fixedQuestion = q.question.replace(/\\\\ffrac\\{/g, '\\\\frac{') // Fix \\ffrac to \\frac\n                    .replace(/\\\\rac\\{/g, '\\\\frac{') // Fix common LaTeX error\n                    .replace(/\\\\fsqrt\\{/g, '\\\\sqrt{') // Fix \\fsqrt to \\sqrt\n                    .replace(/\\\\sqrt\\{rac\\{/g, '\\\\sqrt{\\\\frac{') // Fix sqrt with frac\n                    .replace(/\\$([^$]*rac[^$]*)\\$/g, (_, content)=>{\n                        return '$' + content.replace(/rac\\{/g, 'frac{') + '$';\n                    }).replace(/\\\\fsin\\b/g, '\\\\sin') // Fix \\fsin to \\sin\n                    .replace(/\\\\fcos\\b/g, '\\\\cos') // Fix \\fcos to \\cos\n                    .replace(/\\\\ftan\\b/g, '\\\\tan') // Fix \\ftan to \\tan\n                    .replace(/\\\\flog\\b/g, '\\\\log') // Fix \\flog to \\log\n                    .replace(/\\\\sin\\{/g, '\\\\sin ') // Fix sin function\n                    .replace(/\\\\cos\\{/g, '\\\\cos ') // Fix cos function\n                    .replace(/\\\\tan\\{/g, '\\\\tan ') // Fix tan function\n                    .replace(/\\\\log\\{/g, '\\\\log ') // Fix log function\n                    .replace(/\\\\ln\\{/g, '\\\\ln ') // Fix ln function;\n                    ;\n                    const fixedOptions = q.options.map((opt)=>opt.replace(/\\\\ffrac\\{/g, '\\\\frac{') // Fix \\ffrac to \\frac\n                        .replace(/\\\\rac\\{/g, '\\\\frac{').replace(/\\\\fsqrt\\{/g, '\\\\sqrt{') // Fix \\fsqrt to \\sqrt\n                        .replace(/\\\\sqrt\\{rac\\{/g, '\\\\sqrt{\\\\frac{').replace(/\\$([^$]*rac[^$]*)\\$/g, (_, content)=>{\n                            return '$' + content.replace(/rac\\{/g, 'frac{') + '$';\n                        }).replace(/\\\\fsin\\b/g, '\\\\sin') // Fix \\fsin to \\sin\n                        .replace(/\\\\fcos\\b/g, '\\\\cos') // Fix \\fcos to \\cos\n                        .replace(/\\\\ftan\\b/g, '\\\\tan') // Fix \\ftan to \\tan\n                        .replace(/\\\\flog\\b/g, '\\\\log') // Fix \\flog to \\log\n                        .replace(/\\\\sin\\{/g, '\\\\sin ').replace(/\\\\cos\\{/g, '\\\\cos ').replace(/\\\\tan\\{/g, '\\\\tan ').replace(/\\\\log\\{/g, '\\\\log ').replace(/\\\\ln\\{/g, '\\\\ln '));\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${fixedQuestion}</p>\n                    <div class=\"options\">\n                      ${fixedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();