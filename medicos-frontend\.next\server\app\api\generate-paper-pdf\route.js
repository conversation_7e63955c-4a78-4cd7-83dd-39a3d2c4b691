/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n      @top-center {\n        content: \"${title}\";\n        font-size: 10pt;\n        font-weight: bold;\n      }\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Fix LaTeX formatting issues more comprehensively\n                    // Simple LaTeX fixes - keep it minimal and working\n                    const fixedQuestion = q.question// Fix the main \\ffrac issue - exact patterns from your examples\n                    .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - convert markdown to HTML\n                    .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:300px;height:auto;display:block;margin:10px auto;\" />').replace(/img\\s*[−-]\\s*\\d+\\.jpeg\\s*\\([^)]*\\)/g, ''); // Remove broken image references\n                    const fixedOptions = q.options.map((opt)=>// Simple LaTeX fixes for options - same as questions\n                        opt// Fix the main \\ffrac issue - exact patterns\n                        .replace(/\\\\ffracωLR/g, '\\\\frac{ω}{LR}').replace(/\\\\ffrac1ωCR/g, '\\\\frac{1}{ωCR}').replace(/\\\\ffracLC\\\\ffrac1R/g, '\\\\frac{LC}{\\\\frac{1}{R}}').replace(/\\\\ffracRLC/g, '\\\\frac{R}{LC}').replace(/\\\\ffrac100πMHz/g, '\\\\frac{100}{πMHz}').replace(/\\\\ffrac1000πHz/g, '\\\\frac{1000}{πHz}').replace(/\\\\ffrac11000ohm/g, '\\\\frac{1}{1000ohm}').replace(/\\\\ffrac1Cω/g, '\\\\frac{1}{Cω}')// Fix basic \\ffrac patterns\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{').replace(/\\\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2}').replace(/\\\\ffrac(\\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\\\frac{$1}{$2$3}').replace(/\\\\ffrac([A-Z]+)([A-Z]+)/g, '\\\\frac{$1}{$2}')// Process images - convert markdown to HTML\n                        .replace(/!\\[([^\\]]*)\\]\\(data:image\\/([^;]+);base64,([^)]+)\\)/g, '<img src=\"data:image/$2;base64,$3\" alt=\"$1\" style=\"max-width:200px;height:auto;display:inline-block;margin:5px;\" />').replace(/img\\s*[−-]\\s*\\d+\\.jpeg\\s*\\([^)]*\\)/g, '') // Remove broken image references\n                    );\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${fixedQuestion}</p>\n                    <div class=\"options\">\n                      ${fixedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();