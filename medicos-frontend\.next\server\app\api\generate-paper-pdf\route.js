/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-paper-pdf/route";
exports.ids = ["app/api/generate-paper-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-paper-pdf/route.ts */ \"(rsc)/./src/app/api/generate-paper-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-paper-pdf/route\",\n        pathname: \"/api/generate-paper-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-paper-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-paper-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_paper_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-paper-pdf/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-paper-pdf/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, description, duration, totalMarks, questions, includeAnswers, filename = 'question-paper.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title}</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ],\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: false\n        });\n      }\n    });\n  </script>\n  <style>\n    @page {\n      size: A4;\n      margin: 25mm 15mm 20mm 15mm;\n      @top-center {\n        content: \"${title}\";\n        font-size: 10pt;\n        font-weight: bold;\n      }\n    }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .subject-section { page-break-before: always; }\n    .subject-section:first-child { page-break-before: avoid; }\n    .subject-content {\n      column-count: 2;\n      column-gap: 10mm;\n      column-rule: 1px solid #ccc; /* Add middle line separator */\n      column-rule-style: solid;\n    }\n    .question { break-inside: avoid; margin-bottom: 12px; }\n    .options { margin-left: 16px; }\n    .options p { margin: 2px 0; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading {\n      font-weight: bold;\n      font-size: 12pt;\n      margin: 16px 0 12px;\n      text-align: left;\n      border-bottom: 1px solid #333;\n      padding-bottom: 4px;\n    }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title}</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${(()=>{\n            // Group questions by subject\n            const groupedQuestions = questions.reduce((groups, question)=>{\n                const subject = question.subject || 'General';\n                if (!groups[subject]) {\n                    groups[subject] = [];\n                }\n                groups[subject].push(question);\n                return groups;\n            }, {});\n            // Generate HTML for each subject group\n            return Object.entries(groupedQuestions).map(([subject, subjectQuestions])=>{\n                const subjectHtml = `\n          <div class=\"subject-section\">\n            <div class=\"subject-heading\">Subject: ${subject}</div>\n            <hr style=\"margin: 8px 0; border: none; border-top: 1px solid #333;\" />\n            <div class=\"subject-content\">\n              ${subjectQuestions.map((q, questionIndex)=>{\n                    // Fix LaTeX formatting issues more comprehensively\n                    const fixedQuestion = q.question// Fix the main \\ffrac issue\n                    .replace(/\\\\ffrac\\{/g, '\\\\frac{') // Fix \\ffrac to \\frac\n                    .replace(/\\\\ffrac([^{])/g, '\\\\frac$1') // Fix \\ffrac without braces\n                    .replace(/\\\\rac\\{/g, '\\\\frac{') // Fix common LaTeX error\n                    .replace(/\\\\fsqrt\\{/g, '\\\\sqrt{') // Fix \\fsqrt to \\sqrt\n                    .replace(/\\\\sqrt\\{rac\\{/g, '\\\\sqrt{\\\\frac{') // Fix sqrt with frac\n                    // Fix function names with extra 'f'\n                    .replace(/\\\\fsin\\b/g, '\\\\sin') // Fix \\fsin to \\sin\n                    .replace(/\\\\fcos\\b/g, '\\\\cos') // Fix \\fcos to \\cos\n                    .replace(/\\\\ftan\\b/g, '\\\\tan') // Fix \\ftan to \\tan\n                    .replace(/\\\\flog\\b/g, '\\\\log') // Fix \\flog to \\log\n                    .replace(/\\\\fln\\b/g, '\\\\ln') // Fix \\fln to \\ln\n                    // Fix function braces\n                    .replace(/\\\\sin\\{/g, '\\\\sin ') // Fix sin function\n                    .replace(/\\\\cos\\{/g, '\\\\cos ') // Fix cos function\n                    .replace(/\\\\tan\\{/g, '\\\\tan ') // Fix tan function\n                    .replace(/\\\\log\\{/g, '\\\\log ') // Fix log function\n                    .replace(/\\\\ln\\{/g, '\\\\ln ') // Fix ln function\n                    // Fix content within $ delimiters\n                    .replace(/\\$([^$]*rac[^$]*)\\$/g, (_, content)=>{\n                        return '$' + content.replace(/rac\\{/g, 'frac{') + '$';\n                    })// Fix malformed fractions like \\ffrac100πMHz\n                    .replace(/\\\\ffrac(\\d+)([πα-ωΩ])/g, '\\\\frac{$1}{$2}') // \\ffrac100π -> \\frac{100}{π}\n                    .replace(/\\\\ffrac(\\d+)(\\w+)/g, '\\\\frac{$1}{$2}') // \\ffrac1000Hz -> \\frac{1000}{Hz}\n                    // Clean up any remaining malformed LaTeX\n                    .replace(/([a-zA-Z])\\\\ffrac/g, '$1 \\\\frac') // Fix cases like \"V\\ffrac\"\n                    .replace(/\\\\ffrac([A-Z])/g, '\\\\frac{}{$1}'); // Fix cases like \"\\ffracV\"\n                    const fixedOptions = q.options.map((opt)=>opt// Fix the main \\ffrac issue\n                        .replace(/\\\\ffrac\\{/g, '\\\\frac{') // Fix \\ffrac to \\frac\n                        .replace(/\\\\ffrac([^{])/g, '\\\\frac$1') // Fix \\ffrac without braces\n                        .replace(/\\\\rac\\{/g, '\\\\frac{').replace(/\\\\fsqrt\\{/g, '\\\\sqrt{') // Fix \\fsqrt to \\sqrt\n                        .replace(/\\\\sqrt\\{rac\\{/g, '\\\\sqrt{\\\\frac{')// Fix function names with extra 'f'\n                        .replace(/\\\\fsin\\b/g, '\\\\sin') // Fix \\fsin to \\sin\n                        .replace(/\\\\fcos\\b/g, '\\\\cos') // Fix \\fcos to \\cos\n                        .replace(/\\\\ftan\\b/g, '\\\\tan') // Fix \\ftan to \\tan\n                        .replace(/\\\\flog\\b/g, '\\\\log') // Fix \\flog to \\log\n                        .replace(/\\\\fln\\b/g, '\\\\ln') // Fix \\fln to \\ln\n                        // Fix function braces\n                        .replace(/\\\\sin\\{/g, '\\\\sin ').replace(/\\\\cos\\{/g, '\\\\cos ').replace(/\\\\tan\\{/g, '\\\\tan ').replace(/\\\\log\\{/g, '\\\\log ').replace(/\\\\ln\\{/g, '\\\\ln ')// Fix content within $ delimiters\n                        .replace(/\\$([^$]*rac[^$]*)\\$/g, (_, content)=>{\n                            return '$' + content.replace(/rac\\{/g, 'frac{') + '$';\n                        })// Fix malformed fractions in options\n                        .replace(/\\\\ffrac(\\d+)([πα-ωΩ])/g, '\\\\frac{$1}{$2}') // \\ffrac100π -> \\frac{100}{π}\n                        .replace(/\\\\ffrac(\\d+)(\\w+)/g, '\\\\frac{$1}{$2}') // \\ffrac1000Hz -> \\frac{1000}{Hz}\n                        // Clean up any remaining malformed LaTeX\n                        .replace(/([a-zA-Z])\\\\ffrac/g, '$1 \\\\frac') // Fix cases like \"V\\ffrac\"\n                        .replace(/\\\\ffrac([A-Z])/g, '\\\\frac{}{$1}') // Fix cases like \"\\ffracV\"\n                    );\n                    return `\n                  <div class=\"question\">\n                    <p><strong>${questionIndex + 1}.</strong> ${fixedQuestion}</p>\n                    <div class=\"options\">\n                      ${fixedOptions.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}\n                    </div>\n                  </div>`;\n                }).join('')}\n            </div>\n          </div>`;\n                return subjectHtml;\n            }).join('');\n        })()}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait for KaTeX to load and render math\n        await page.waitForFunction(()=>{\n            return window.renderMathInElement !== undefined;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Trigger math rendering manually if needed\n        await page.evaluate(()=>{\n            if (window.renderMathInElement) {\n                window.renderMathInElement(document.body, {\n                    delimiters: [\n                        {\n                            left: '$$',\n                            right: '$$',\n                            display: true\n                        },\n                        {\n                            left: '$',\n                            right: '$',\n                            display: false\n                        },\n                        {\n                            left: '\\\\(',\n                            right: '\\\\)',\n                            display: false\n                        },\n                        {\n                            left: '\\\\[',\n                            right: '\\\\]',\n                            display: true\n                        }\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false\n                });\n            }\n        });\n        // Wait for rendering to complete\n        await page.waitForFunction(()=>{\n            const mathElements = document.querySelectorAll('script[type=\"math/tex\"]');\n            const katexElements = document.querySelectorAll('.katex');\n            return mathElements.length === 0 || katexElements.length > 0;\n        }, {\n            timeout: 5000\n        }).catch(()=>{});\n        // Extra delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-paper-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-paper-pdf%2Froute&page=%2Fapi%2Fgenerate-paper-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-paper-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();