#!/usr/bin/env python3
"""
PDF Diagnostic Tool for Question Extraction Issues

This script helps diagnose why PDF extraction is only getting 5-6 questions 
instead of 300-400 questions from large PDFs.
"""

import os
import sys
import time
import json
import re
from question_extractor import QuestionExtractor

def analyze_pdf_structure(pdf_path):
    """
    Analyze PDF structure to understand content distribution
    """
    print("🔍 [PDF_ANALYSIS] Analyzing PDF structure...")
    
    try:
        # Initialize extractor
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Extract OCR data
        print("📄 [OCR_ANALYSIS] Extracting OCR data...")
        ocr_data = extractor.extract_ocr_data_from_pdf(pdf_path)
        
        full_text = ocr_data['full_text']
        all_images = ocr_data['all_images']
        
        print(f"✅ [OCR_RESULTS] Text length: {len(full_text):,} characters")
        print(f"✅ [OCR_RESULTS] Images found: {len(all_images)}")
        
        # Analyze text patterns
        print("\n🔍 [PATTERN_ANALYSIS] Analyzing question patterns...")
        
        patterns = {
            'numbered_questions': r'\d+\.\s*[A-Z]',
            'q_format': r'Q\d+[:\.]',
            'question_word': r'Question\s*\d+',
            'question_marks': r'\?',
            'option_a': r'\ba\)',
            'option_b': r'\bb\)',
            'option_c': r'\bc\)',
            'option_d': r'\bd\)',
            'answer_keys': r'\d+\.\s*[ABCD]',
        }
        
        pattern_counts = {}
        for name, pattern in patterns.items():
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            pattern_counts[name] = len(matches)
            print(f"   {name}: {len(matches)} matches")
        
        # Estimate questions based on patterns
        estimated_questions = max(
            pattern_counts['numbered_questions'],
            pattern_counts['q_format'],
            pattern_counts['question_word'],
            pattern_counts['question_marks'] // 4  # Assume some false positives
        )
        
        print(f"\n📊 [ESTIMATION] Estimated questions in PDF: {estimated_questions}")
        
        # Text quality analysis
        print("\n🔍 [TEXT_QUALITY] Analyzing text quality...")
        
        # Check for common OCR issues
        ocr_issues = {
            'garbled_text': len(re.findall(r'[^\w\s\.\?\!\,\;\:\(\)\[\]\-\+\=\*\/\%\$\#\@\&]', full_text)),
            'repeated_chars': len(re.findall(r'(.)\1{5,}', full_text)),
            'missing_spaces': len(re.findall(r'[a-z][A-Z]', full_text)),
            'broken_words': len(re.findall(r'\b\w{1,2}\b', full_text))
        }
        
        for issue, count in ocr_issues.items():
            print(f"   {issue}: {count} instances")
        
        # Sample text analysis
        print(f"\n📄 [TEXT_SAMPLE] First 2000 characters:")
        print(f"{full_text[:2000]}...")
        
        print(f"\n📄 [TEXT_SAMPLE] Last 2000 characters:")
        print(f"...{full_text[-2000:]}")
        
        return {
            'text_length': len(full_text),
            'image_count': len(all_images),
            'pattern_counts': pattern_counts,
            'estimated_questions': estimated_questions,
            'ocr_issues': ocr_issues,
            'full_text': full_text
        }
        
    except Exception as e:
        print(f"❌ [PDF_ANALYSIS_ERROR] Error analyzing PDF: {e}")
        return None

def test_extraction_methods(pdf_path, analysis_data):
    """
    Test different extraction methods to see which works best
    """
    print("\n🧪 [EXTRACTION_TEST] Testing different extraction methods...")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        full_text = analysis_data['full_text']
        
        # Test 1: Standard extraction
        print("\n🔬 [TEST_1] Standard extraction method...")
        try:
            result1 = extractor.extract_questions_from_pdf(pdf_path)
            questions1 = json.loads(extractor._clean_json_response(result1))
            print(f"✅ [TEST_1] Standard method: {len(questions1)} questions")
        except Exception as e:
            print(f"❌ [TEST_1] Standard method failed: {e}")
            questions1 = []
        
        # Test 2: Aggressive extraction
        print("\n🔬 [TEST_2] Aggressive extraction method...")
        try:
            result2 = extractor._try_aggressive_extraction(full_text)
            questions2 = json.loads(extractor._clean_json_response(result2))
            print(f"✅ [TEST_2] Aggressive method: {len(questions2)} questions")
        except Exception as e:
            print(f"❌ [TEST_2] Aggressive method failed: {e}")
            questions2 = []
        
        # Test 3: Pattern-based extraction
        print("\n🔬 [TEST_3] Pattern-based extraction method...")
        try:
            result3 = extractor._try_pattern_based_extraction(full_text)
            questions3 = json.loads(extractor._clean_json_response(result3))
            print(f"✅ [TEST_3] Pattern-based method: {len(questions3)} questions")
        except Exception as e:
            print(f"❌ [TEST_3] Pattern-based method failed: {e}")
            questions3 = []
        
        # Test 4: Enhanced chunking (if text is large enough)
        if len(full_text) > 200000:
            print("\n🔬 [TEST_4] Enhanced chunking method...")
            try:
                result4 = extractor._process_large_text_in_chunks_enhanced(full_text, "")
                questions4 = json.loads(result4)
                print(f"✅ [TEST_4] Enhanced chunking: {len(questions4)} questions")
            except Exception as e:
                print(f"❌ [TEST_4] Enhanced chunking failed: {e}")
                questions4 = []
        else:
            questions4 = []
        
        # Compare results
        print(f"\n📊 [COMPARISON] Extraction method comparison:")
        print(f"   Standard method: {len(questions1)} questions")
        print(f"   Aggressive method: {len(questions2)} questions")
        print(f"   Pattern-based method: {len(questions3)} questions")
        print(f"   Enhanced chunking: {len(questions4)} questions")
        
        # Find best method
        methods = [
            ('Standard', questions1),
            ('Aggressive', questions2),
            ('Pattern-based', questions3),
            ('Enhanced chunking', questions4)
        ]
        
        best_method, best_questions = max(methods, key=lambda x: len(x[1]))
        print(f"\n🏆 [BEST_METHOD] Best method: {best_method} with {len(best_questions)} questions")
        
        # Show sample questions from best method
        if best_questions:
            print(f"\n📝 [SAMPLE_QUESTIONS] Sample from best method:")
            for i, q in enumerate(best_questions[:3]):
                q_text = q.get('question', q.get('content', 'No text'))[:100]
                print(f"   {i+1}. {q_text}...")
        
        return best_method, len(best_questions)
        
    except Exception as e:
        print(f"❌ [EXTRACTION_TEST_ERROR] Error testing extraction methods: {e}")
        return None, 0

def main():
    """Main diagnostic function"""
    if len(sys.argv) != 2:
        print("Usage: python pdf_diagnostic.py <path_to_pdf>")
        print("Example: python pdf_diagnostic.py large_document.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        sys.exit(1)
    
    file_size = os.path.getsize(pdf_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"🚀 PDF Extraction Diagnostic Tool")
    print(f"📄 File: {os.path.basename(pdf_path)}")
    print(f"📊 Size: {file_size_mb:.1f}MB ({file_size:,} bytes)")
    print("=" * 60)
    
    # Step 1: Analyze PDF structure
    analysis_data = analyze_pdf_structure(pdf_path)
    if not analysis_data:
        print("❌ PDF analysis failed. Cannot continue.")
        sys.exit(1)
    
    # Step 2: Test extraction methods
    best_method, best_count = test_extraction_methods(pdf_path, analysis_data)
    
    # Step 3: Provide recommendations
    print(f"\n💡 [RECOMMENDATIONS] Based on analysis:")
    
    estimated = analysis_data['estimated_questions']
    if best_count < estimated * 0.1:  # Less than 10% of estimated
        print(f"❌ CRITICAL: Only {best_count} questions extracted vs {estimated} estimated")
        print(f"   Possible issues:")
        print(f"   - OCR quality is poor (check text samples above)")
        print(f"   - Questions are in non-standard format")
        print(f"   - PDF contains mostly images without text")
        print(f"   - AI model is not recognizing question patterns")
        
        print(f"\n🔧 Suggested fixes:")
        print(f"   1. Try preprocessing PDF (OCR enhancement)")
        print(f"   2. Use different AI model or prompt")
        print(f"   3. Implement custom pattern matching")
        print(f"   4. Split PDF into smaller sections")
        
    elif best_count < estimated * 0.5:  # Less than 50% of estimated
        print(f"⚠️ MODERATE: {best_count} questions extracted vs {estimated} estimated")
        print(f"   The {best_method} method works best")
        print(f"   Consider using this method as default")
        
    else:
        print(f"✅ GOOD: {best_count} questions extracted vs {estimated} estimated")
        print(f"   The {best_method} method works well")
    
    print(f"\n🎯 [CONCLUSION] Diagnostic complete!")

if __name__ == "__main__":
    main()
