#!/usr/bin/env python3
"""
Chunking Strategy Test Tool

This script tests different chunking strategies to find the optimal approach
for extracting maximum questions from large PDFs.
"""

import os
import sys
import time
import json
from question_extractor import QuestionExtractor

def test_chunking_strategy(full_text, strategy_name, chunk_size, overlap_size, extractor):
    """
    Test a specific chunking strategy
    """
    print(f"\n🧪 [CHUNKING_TEST] Testing {strategy_name}...")
    print(f"   Chunk size: {chunk_size:,} chars")
    print(f"   Overlap size: {overlap_size:,} chars")
    
    text_length = len(full_text)
    total_chunks = max(1, (text_length + chunk_size - overlap_size - 1) // (chunk_size - overlap_size))
    
    print(f"   Total chunks: {total_chunks}")
    
    all_questions = []
    processed_chunks = 0
    failed_chunks = 0
    start_time = time.time()
    
    for i in range(0, text_length, chunk_size - overlap_size):
        chunk_start = i
        chunk_end = min(i + chunk_size, text_length)
        chunk_text = full_text[chunk_start:chunk_end]
        
        processed_chunks += 1
        
        # Simple extraction prompt for testing
        chunk_prompt = f"""
        Extract ALL multiple-choice questions from this text chunk.
        
        Return JSON array format:
        [
          {{
            "question": "Question text",
            "options": {{
              "A": "Option A",
              "B": "Option B", 
              "C": "Option C",
              "D": "Option D"
            }},
            "answer": "A",
            "type": "mcq",
            "difficulty": "medium"
          }}
        ]
        
        Text chunk:
        {chunk_text}
        """
        
        try:
            if extractor.ai_provider == 'gemini':
                chunk_result = extractor._extract_with_gemini(chunk_prompt)
            else:
                chunk_result = extractor._extract_with_mistral(chunk_prompt)
            
            cleaned_result = extractor._clean_json_response(chunk_result)
            chunk_questions = json.loads(cleaned_result)
            
            if isinstance(chunk_questions, list):
                all_questions.extend(chunk_questions)
                print(f"   Chunk {processed_chunks}: {len(chunk_questions)} questions")
            else:
                failed_chunks += 1
                print(f"   Chunk {processed_chunks}: Failed (invalid format)")
                
        except Exception as e:
            failed_chunks += 1
            print(f"   Chunk {processed_chunks}: Failed ({str(e)[:50]}...)")
    
    # Remove duplicates
    unique_questions = extractor._remove_duplicate_questions_enhanced(all_questions)
    
    processing_time = time.time() - start_time
    
    print(f"✅ [{strategy_name}] Results:")
    print(f"   Total questions: {len(all_questions)}")
    print(f"   Unique questions: {len(unique_questions)}")
    print(f"   Failed chunks: {failed_chunks}/{processed_chunks}")
    print(f"   Processing time: {processing_time:.1f}s")
    print(f"   Questions per second: {len(unique_questions) / processing_time:.1f}")
    
    return {
        'strategy': strategy_name,
        'total_questions': len(all_questions),
        'unique_questions': len(unique_questions),
        'failed_chunks': failed_chunks,
        'total_chunks': processed_chunks,
        'processing_time': processing_time,
        'questions_per_second': len(unique_questions) / processing_time if processing_time > 0 else 0
    }

def main():
    """Main function to test different chunking strategies"""
    if len(sys.argv) != 2:
        print("Usage: python chunking_test.py <path_to_pdf>")
        print("Example: python chunking_test.py large_document.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        sys.exit(1)
    
    print(f"🚀 Chunking Strategy Test Tool")
    print(f"📄 File: {os.path.basename(pdf_path)}")
    print("=" * 60)
    
    # Initialize extractor and get text
    print("📄 Extracting text from PDF...")
    extractor = QuestionExtractor(ai_provider='gemini')
    
    try:
        ocr_data = extractor.extract_ocr_data_from_pdf(pdf_path)
        full_text = ocr_data['full_text']
        text_length = len(full_text)
        
        print(f"✅ Text extracted: {text_length:,} characters")
        
        if text_length < 100000:
            print("⚠️ Text is too small for chunking tests. Use a larger PDF.")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error extracting text: {e}")
        sys.exit(1)
    
    # Define chunking strategies to test
    strategies = [
        # (name, chunk_size, overlap_size)
        ("Conservative", 400000, 50000),    # Large chunks, small overlap
        ("Balanced", 300000, 75000),        # Medium chunks, medium overlap
        ("Aggressive", 200000, 100000),     # Small chunks, large overlap
        ("High Overlap", 250000, 125000),   # Medium chunks, 50% overlap
        ("Small Chunks", 150000, 75000),    # Small chunks, 50% overlap
        ("Tiny Chunks", 100000, 50000),     # Very small chunks, 50% overlap
    ]
    
    results = []
    
    # Test each strategy
    for strategy_name, chunk_size, overlap_size in strategies:
        try:
            result = test_chunking_strategy(
                full_text, strategy_name, chunk_size, overlap_size, extractor
            )
            results.append(result)
        except Exception as e:
            print(f"❌ Strategy {strategy_name} failed: {e}")
    
    # Compare results
    print(f"\n📊 [COMPARISON] Chunking Strategy Comparison:")
    print(f"{'Strategy':<15} {'Questions':<10} {'Unique':<8} {'Failed':<8} {'Time':<8} {'Q/s':<8}")
    print("-" * 70)
    
    for result in results:
        print(f"{result['strategy']:<15} "
              f"{result['total_questions']:<10} "
              f"{result['unique_questions']:<8} "
              f"{result['failed_chunks']:<8} "
              f"{result['processing_time']:<8.1f} "
              f"{result['questions_per_second']:<8.1f}")
    
    # Find best strategy
    if results:
        best_result = max(results, key=lambda x: x['unique_questions'])
        
        print(f"\n🏆 [BEST_STRATEGY] Best strategy: {best_result['strategy']}")
        print(f"   Unique questions: {best_result['unique_questions']}")
        print(f"   Success rate: {((best_result['total_chunks'] - best_result['failed_chunks']) / best_result['total_chunks'] * 100):.1f}%")
        print(f"   Processing speed: {best_result['questions_per_second']:.1f} questions/second")
        
        # Provide recommendations
        print(f"\n💡 [RECOMMENDATIONS]:")
        if best_result['unique_questions'] < 50:
            print(f"❌ All strategies performed poorly ({best_result['unique_questions']} questions)")
            print(f"   This suggests fundamental issues with:")
            print(f"   - PDF text quality (OCR issues)")
            print(f"   - Question format recognition")
            print(f"   - AI model performance")
            print(f"   Try the diagnostic tool: python pdf_diagnostic.py {pdf_path}")
        else:
            print(f"✅ Best strategy found: {best_result['strategy']}")
            print(f"   Implement this strategy in the main extraction code")
            print(f"   Consider using chunk size: {chunk_size:,} with overlap: {overlap_size:,}")
    
    print(f"\n🎯 [CONCLUSION] Chunking strategy test complete!")

if __name__ == "__main__":
    main()
