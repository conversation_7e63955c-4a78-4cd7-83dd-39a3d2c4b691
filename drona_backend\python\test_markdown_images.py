#!/usr/bin/env python3
"""
Test markdown image format generation
"""

from question_extractor import QuestionExtractor

def test_markdown_image_generation():
    """Test that images are generated in markdown format"""
    
    print("🧪 Testing markdown image generation...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Mock OCR data with sample image
    extractor.current_ocr_data = {
        'all_images': {
            'img-132.jpeg': '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAEpAd0DASIAAhEBAxEB'
        }
    }
    
    # Test text with image reference
    test_text = "In the circuit shown below, the key K is closed at t=0. ![img-132.jpeg](img-132.jpeg) The current through the battery is measured."
    
    print(f"📝 Input text: {test_text}")
    
    # Process the text
    result = extractor._process_images_in_text(test_text)
    
    print(f"📄 Output text: {result}")
    
    # Check if markdown format is generated
    if '![' in result and '](data:image/jpeg;base64,' in result:
        print("✅ Markdown image format generated successfully!")
        
        # Extract the markdown image
        import re
        markdown_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        matches = re.findall(markdown_pattern, result)
        
        if matches:
            for match in matches:
                alt_text = match[0]
                image_src = match[1]
                print(f"   Alt text: {alt_text}")
                print(f"   Image src: {image_src[:50]}...")
                
                # Check if it's a valid data URL
                if image_src.startswith('data:image/jpeg;base64,'):
                    print("   ✅ Valid data URL format")
                else:
                    print("   ❌ Invalid data URL format")
    else:
        print("❌ Markdown image format not generated")
    
    return result

def test_full_cleaning():
    """Test the full text cleaning process"""
    
    print("\n🧪 Testing full text cleaning...")
    
    extractor = QuestionExtractor(ai_provider='gemini')
    
    # Mock OCR data
    extractor.current_ocr_data = {
        'all_images': {
            'img-108.jpeg': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        }
    }
    
    # Test text with image and other issues
    test_text = "In the circuit shown below ![img-108.jpeg](img-108.jpeg) the amplitude will be nearest to 225(c) f=1/(2π√LC) ∴ f=1600 Hz"
    
    print(f"📝 Input: {test_text}")
    
    # Clean the text
    cleaned = extractor._clean_text_for_json(test_text)
    
    print(f"📄 Cleaned: {cleaned}")
    
    # Check results
    if '![' in cleaned and 'data:image/jpeg;base64,' in cleaned:
        print("✅ Image converted to markdown")
    else:
        print("❌ Image not converted")
    
    if '225(c)' not in cleaned:
        print("✅ Solution contamination removed")
    else:
        print("❌ Solution contamination still present")
    
    if 'f=1600 Hz' not in cleaned:
        print("✅ Calculation text removed")
    else:
        print("❌ Calculation text still present")

def main():
    """Main test function"""
    
    print("🚀 Markdown Image Format Test")
    print("=" * 50)
    
    try:
        # Test markdown generation
        test_markdown_image_generation()
        
        # Test full cleaning
        test_full_cleaning()
        
        print(f"\n✅ Tests completed!")
        print(f"\n💡 Key changes:")
        print(f"   ✅ Backend now generates markdown format: ![alt](data:image/jpeg;base64,...)")
        print(f"   ✅ Frontend already supports markdown image extraction")
        print(f"   ✅ Should work without frontend changes")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
