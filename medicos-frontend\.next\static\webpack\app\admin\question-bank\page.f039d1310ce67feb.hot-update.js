"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n * Also handles cases where the data URL prefix is duplicated\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // Handle duplicated data URL prefixes (e.g., \"data:image/jpeg;base64,data:image/jpeg;base64,...\")\n    const duplicatedPrefixPattern = /^(data:image\\/[^;]+;base64,)(data:image\\/[^;]+;base64,)/;\n    if (duplicatedPrefixPattern.test(base64String)) {\n        // Remove the first occurrence of the duplicated prefix\n        base64String = base64String.replace(duplicatedPrefixPattern, '$2');\n    }\n    // Handle cases where there might be multiple data: prefixes\n    const multiplePrefixPattern = /^(data:image\\/[^;]+;base64,)+/;\n    const prefixMatch = base64String.match(multiplePrefixPattern);\n    if (prefixMatch) {\n        // Extract just the base64 part after all prefixes\n        const prefixLength = prefixMatch[0].length;\n        const base64Part = base64String.substring(prefixLength);\n        // Get the last valid prefix\n        const lastPrefixMatch = prefixMatch[0].match(/data:image\\/[^;]+;base64,$/);\n        if (lastPrefixMatch) {\n            return lastPrefixMatch[0] + base64Part;\n        }\n    }\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to JPEG for better compatibility\n    return \"data:image/jpeg;base64,\".concat(base64String);\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // First, look for HTML img tags: <img src=\"data:image/type;base64,...\" alt=\"...\" />\n    const htmlImagePattern = /<img\\s+[^>]*src=[\"']([^\"']+)[\"'][^>]*(?:alt=[\"']([^\"']*)[\"'])?[^>]*\\/?>/gi;\n    const htmlMatches = [\n        ...text.matchAll(htmlImagePattern)\n    ];\n    if (htmlMatches.length > 0) {\n        htmlMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            let imageSrc = match[1];\n            const altText = match[2] || \"Image \".concat(images.length + 1);\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = \"extracted-image-html-\".concat(index);\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the HTML img tag from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for markdown-style image syntax: ![alt](data:image/type;base64,...)\n    const markdownImagePattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n    const markdownMatches = [\n        ...cleanText.matchAll(markdownImagePattern)\n    ]; // Use cleanText to avoid already processed HTML images\n    if (markdownMatches.length > 0) {\n        markdownMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            const altText = match[1] || \"Image \".concat(images.length + 1);\n            let imageSrc = match[2];\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = \"extracted-image-markdown-\".concat(index);\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the markdown image syntax from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for complete data URLs (these take priority over raw base64)\n    const dataUrlPattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;\n    const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images\n    if (dataUrlMatches) {\n        dataUrlMatches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-dataurl-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the complete data URL from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Finally, look for raw base64 strings (only if they're not part of already processed content)\n    const rawBase64Pattern = /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g;\n    const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content\n    if (rawBase64Matches) {\n        rawBase64Matches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-raw-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the raw base64 string from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageUtils.ts\n"));

/***/ })

});