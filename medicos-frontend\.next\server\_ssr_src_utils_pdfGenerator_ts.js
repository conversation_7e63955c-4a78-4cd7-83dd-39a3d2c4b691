"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_utils_pdfGenerator_ts";
exports.ids = ["_ssr_src_utils_pdfGenerator_ts"];
exports.modules = {

/***/ "(ssr)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n * Also handles cases where the data URL prefix is duplicated\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // Handle duplicated data URL prefixes (e.g., \"data:image/jpeg;base64,data:image/jpeg;base64,...\")\n    const duplicatedPrefixPattern = /^(data:image\\/[^;]+;base64,)(data:image\\/[^;]+;base64,)/;\n    if (duplicatedPrefixPattern.test(base64String)) {\n        // Remove the first occurrence of the duplicated prefix\n        base64String = base64String.replace(duplicatedPrefixPattern, '$2');\n    }\n    // Handle cases where there might be multiple data: prefixes\n    const multiplePrefixPattern = /^(data:image\\/[^;]+;base64,)+/;\n    const prefixMatch = base64String.match(multiplePrefixPattern);\n    if (prefixMatch) {\n        // Extract just the base64 part after all prefixes\n        const prefixLength = prefixMatch[0].length;\n        const base64Part = base64String.substring(prefixLength);\n        // Get the last valid prefix\n        const lastPrefixMatch = prefixMatch[0].match(/data:image\\/[^;]+;base64,$/);\n        if (lastPrefixMatch) {\n            return lastPrefixMatch[0] + base64Part;\n        }\n    }\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to JPEG for better compatibility\n    return `data:image/jpeg;base64,${base64String}`;\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // First, look for HTML img tags: <img src=\"data:image/type;base64,...\" alt=\"...\" />\n    const htmlImagePattern = /<img\\s+[^>]*src=[\"']([^\"']+)[\"'][^>]*(?:alt=[\"']([^\"']*)[\"'])?[^>]*\\/?>/gi;\n    const htmlMatches = [\n        ...text.matchAll(htmlImagePattern)\n    ];\n    if (htmlMatches.length > 0) {\n        htmlMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            let imageSrc = match[1];\n            const altText = match[2] || `Image ${images.length + 1}`;\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = `extracted-image-html-${index}`;\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the HTML img tag from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for markdown-style image syntax: ![alt](data:image/type;base64,...)\n    const markdownImagePattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n    const markdownMatches = [\n        ...cleanText.matchAll(markdownImagePattern)\n    ]; // Use cleanText to avoid already processed HTML images\n    if (markdownMatches.length > 0) {\n        markdownMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            const altText = match[1] || `Image ${images.length + 1}`;\n            let imageSrc = match[2];\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = `extracted-image-markdown-${index}`;\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the markdown image syntax from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for complete data URLs (these take priority over raw base64)\n    const dataUrlPattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;\n    const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images\n    if (dataUrlMatches) {\n        dataUrlMatches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = `extracted-image-dataurl-${index}`;\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: `Extracted image ${images.length + 1}`\n                });\n                // Remove the complete data URL from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Finally, look for raw base64 strings (only if they're not part of already processed content)\n    const rawBase64Pattern = /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g;\n    const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content\n    if (rawBase64Matches) {\n        rawBase64Matches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = `extracted-image-raw-${index}`;\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: `Extracted image ${images.length + 1}`\n                });\n                // Remove the raw base64 string from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/imageUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/mathRenderer.ts":
/*!***********************************!*\
  !*** ./src/utils/mathRenderer.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MathRenderer: () => (/* binding */ MathRenderer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var katex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.mjs\");\n/**\n * Production-ready KaTeX Math Renderer for PDF Generation\n * \n * This utility provides server-side KaTeX rendering for mathematical expressions\n * in PDF documents, with proper fallback mechanisms and error handling.\n */ \nclass MathRenderer {\n    static{\n        this.DEFAULT_OPTIONS = {\n            displayMode: false,\n            throwOnError: false,\n            errorColor: '#cc0000',\n            strict: false,\n            trust: false,\n            macros: {},\n            colorIsTextColor: true,\n            maxSize: 500,\n            maxExpand: 1000\n        };\n    }\n    /**\n   * Render LaTeX expression to HTML using KaTeX\n   */ static renderToHTML(expression, options = {}) {\n        const mergedOptions = {\n            ...this.DEFAULT_OPTIONS,\n            ...options\n        };\n        try {\n            console.log(`Rendering LaTeX expression: ${expression}`);\n            // Clean the expression\n            const cleanExpression = this.cleanExpression(expression);\n            if (!cleanExpression.trim()) {\n                return {\n                    html: '',\n                    success: false,\n                    error: 'Empty expression',\n                    fallbackText: ''\n                };\n            }\n            // Render using KaTeX\n            const html = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(cleanExpression, mergedOptions);\n            console.log(`Successfully rendered: ${cleanExpression}`);\n            return {\n                html,\n                success: true\n            };\n        } catch (error) {\n            console.warn(`KaTeX rendering failed for \"${expression}\":`, error);\n            // Generate fallback text\n            const fallbackText = this.generateFallbackText(expression);\n            return {\n                html: `<span style=\"color: ${mergedOptions.errorColor};\">${fallbackText}</span>`,\n                success: false,\n                error: error instanceof Error ? error.message : String(error),\n                fallbackText\n            };\n        }\n    }\n    /**\n   * Clean and prepare LaTeX expression for rendering\n   */ static cleanExpression(expression) {\n        return expression.trim()// Remove outer $ delimiters if present\n        .replace(/^\\$+/, '').replace(/\\$+$/, '')// Normalize whitespace\n        .replace(/\\s+/g, ' ').trim();\n    }\n    /**\n   * Generate fallback text when KaTeX rendering fails\n   */ static generateFallbackText(expression) {\n        // Use comprehensive LaTeX to Unicode converter as fallback\n        let fallbackText = expression// Remove delimiters\n        .replace(/^\\$+/, '').replace(/\\$+$/, '')// Arrows and implications\n        .replace(/\\\\Rightarrow/g, '⇒').replace(/\\\\Leftarrow/g, '⇐').replace(/\\\\Leftrightarrow/g, '⇔').replace(/\\\\rightarrow/g, '→').replace(/\\\\leftarrow/g, '←').replace(/\\\\leftrightarrow/g, '↔')// Comparison operators\n        .replace(/\\\\geq/g, '≥').replace(/\\\\leq/g, '≤').replace(/\\\\neq/g, '≠').replace(/\\\\approx/g, '≈').replace(/\\\\equiv/g, '≡').replace(/\\\\sim/g, '∼')// Binary operators\n        .replace(/\\\\pm/g, '±').replace(/\\\\mp/g, '∓').replace(/\\\\times/g, '×').replace(/\\\\div/g, '÷').replace(/\\\\cdot/g, '⋅')// Greek letters (common ones)\n        .replace(/\\\\alpha/g, 'α').replace(/\\\\beta/g, 'β').replace(/\\\\gamma/g, 'γ').replace(/\\\\delta/g, 'δ').replace(/\\\\epsilon/g, 'ε').replace(/\\\\theta/g, 'θ').replace(/\\\\lambda/g, 'λ').replace(/\\\\mu/g, 'μ').replace(/\\\\pi/g, 'π').replace(/\\\\sigma/g, 'σ').replace(/\\\\phi/g, 'φ').replace(/\\\\omega/g, 'ω')// Large operators\n        .replace(/\\\\sum/g, '∑').replace(/\\\\prod/g, '∏').replace(/\\\\int/g, '∫')// Set theory\n        .replace(/\\\\in/g, '∈').replace(/\\\\notin/g, '∉').replace(/\\\\subset/g, '⊂').replace(/\\\\supset/g, '⊃').replace(/\\\\cup/g, '∪').replace(/\\\\cap/g, '∩').replace(/\\\\forall/g, '∀').replace(/\\\\exists/g, '∃').replace(/\\\\emptyset/g, '∅')// Special symbols\n        .replace(/\\\\infty/g, '∞').replace(/\\\\partial/g, '∂').replace(/\\\\nabla/g, '∇')// Number sets\n        .replace(/\\\\mathbb{N}/g, 'ℕ').replace(/\\\\mathbb{Z}/g, 'ℤ').replace(/\\\\mathbb{Q}/g, 'ℚ').replace(/\\\\mathbb{R}/g, 'ℝ').replace(/\\\\mathbb{C}/g, 'ℂ')// Functions and operations\n        .replace(/\\\\sqrt{([^}]+)}/g, '√($1)').replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)')// Delimiters\n        .replace(/\\\\left\\(/g, '(').replace(/\\\\right\\)/g, ')').replace(/\\\\left\\[/g, '[').replace(/\\\\right\\]/g, ']').replace(/\\\\left\\{/g, '{').replace(/\\\\right\\}/g, '}').replace(/\\\\left\\|/g, '|').replace(/\\\\right\\|/g, '|').replace(/\\\\left/g, '').replace(/\\\\right/g, '')// Dots\n        .replace(/\\\\ldots/g, '...').replace(/\\\\cdots/g, '⋯')// Superscripts and subscripts\n        .replace(/\\^{([^}]+)}/g, '^($1)').replace(/_{([^}]+)}/g, '_($1)').replace(/\\^(\\w)/g, '^$1').replace(/_(\\w)/g, '_$1')// Text formatting\n        .replace(/\\\\mathrm{([^}]+)}/g, '$1').replace(/\\\\mathbf{([^}]+)}/g, '$1').replace(/\\\\text{([^}]+)}/g, '$1')// Clean up remaining LaTeX commands\n        .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1').replace(/\\\\[a-zA-Z]+/g, '')// Normalize spaces\n        .replace(/\\s+/g, ' ').trim();\n        return fallbackText || expression;\n    }\n    /**\n   * Extract math expressions from text\n   */ static extractMathExpressions(text) {\n        const expressions = [];\n        // Match both inline ($...$) and block ($$...$$) math\n        const regex = /(\\$\\$[\\s\\S]*?\\$\\$|\\$[^$]*?\\$)/g;\n        let match;\n        while((match = regex.exec(text)) !== null){\n            const fullMatch = match[0];\n            const isBlock = fullMatch.startsWith('$$') && fullMatch.endsWith('$$');\n            let expression;\n            if (isBlock) {\n                expression = fullMatch.slice(2, -2).trim();\n            } else {\n                expression = fullMatch.slice(1, -1).trim();\n            }\n            expressions.push({\n                expression,\n                startIndex: match.index,\n                endIndex: match.index + fullMatch.length,\n                isBlock\n            });\n        }\n        return expressions;\n    }\n    /**\n   * Process text with math expressions and return rendered HTML\n   */ static processTextWithMath(text, options = {}) {\n        const expressions = this.extractMathExpressions(text);\n        if (expressions.length === 0) {\n            return text;\n        }\n        let result = '';\n        let lastIndex = 0;\n        for (const expr of expressions){\n            // Add text before the math expression\n            result += text.substring(lastIndex, expr.startIndex);\n            // Render the math expression\n            const rendered = this.renderToHTML(expr.expression, {\n                ...options,\n                displayMode: expr.isBlock\n            });\n            if (rendered.success) {\n                result += rendered.html;\n            } else {\n                // Use fallback text\n                result += rendered.fallbackText || expr.expression;\n            }\n            lastIndex = expr.endIndex;\n        }\n        // Add remaining text\n        result += text.substring(lastIndex);\n        return result;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MathRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/mathRenderer.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/pdfGenerator.ts":
/*!***********************************!*\
  !*** ./src/utils/pdfGenerator.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PDFGenerator: () => (/* binding */ PDFGenerator),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(ssr)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/imageUtils */ \"(ssr)/./src/utils/imageUtils.ts\");\n/* harmony import */ var _utils_svgMathRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/svgMathRenderer */ \"(ssr)/./src/utils/svgMathRenderer.ts\");\n\n\n\nclass PDFGenerator {\n    constructor(){\n        // NEET exam standard font sizes for professional appearance\n        this.FONT_SIZES = {\n            TITLE: 14,\n            PART_HEADER: 12,\n            SECTION_HEADER: 11,\n            COLLEGE_NAME: 12,\n            COLLEGE_ADDRESS: 9,\n            EXAM_DETAILS: 10,\n            QUESTION_NUMBER: 10,\n            QUESTION_TEXT: 10,\n            OPTION_TEXT: 9,\n            ANSWER_TEXT: 10,\n            SOLUTION_HEADER: 10,\n            SOLUTION_TEXT: 9,\n            HINT_TEXT: 9,\n            MARKING_SCHEME: 9,\n            INSTRUCTIONS: 8,\n            WATERMARK: 60,\n            LOGO: 8,\n            FOOTER: 8\n        };\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]('p', 'mm', 'a4');\n        this.pageWidth = this.doc.internal.pageSize.getWidth();\n        this.pageHeight = this.doc.internal.pageSize.getHeight();\n        this.margin = 20;\n        this.currentY = this.margin;\n        // Two-column layout setup with NEET exam standards\n        this.columnGap = 8; // Smaller gap for more space\n        this.columnWidth = (this.pageWidth - this.margin * 2 - this.columnGap) / 2;\n        this.leftColumnX = this.margin;\n        this.rightColumnX = this.margin + this.columnWidth + this.columnGap;\n        this.currentColumn = 'left';\n        this.leftColumnY = this.margin;\n        this.rightColumnY = this.margin;\n        this.pageContentStartY = this.margin;\n        // Keep contentWidth for backward compatibility\n        this.contentWidth = this.pageWidth - this.margin * 2;\n        // Set default font to Times\n        try {\n            this.doc.setFont('times', 'normal');\n        } catch (error) {\n            console.warn('Times font not available, using default');\n            this.doc.setFont('helvetica', 'normal');\n        }\n    }\n    // Two-column layout management methods\n    getCurrentColumnX() {\n        return this.currentColumn === 'left' ? this.leftColumnX : this.rightColumnX;\n    }\n    getCurrentColumnY() {\n        return this.currentColumn === 'left' ? this.leftColumnY : this.rightColumnY;\n    }\n    updateCurrentColumnY(newY) {\n        if (this.currentColumn === 'left') {\n            this.leftColumnY = newY;\n        } else {\n            this.rightColumnY = newY;\n        }\n    }\n    switchToNextColumn() {\n        if (this.currentColumn === 'left') {\n            this.currentColumn = 'right';\n        } else {\n            // Both columns filled, need new page\n            this.currentColumn = 'left';\n        }\n    }\n    moveToNextAvailablePosition() {\n        if (this.currentColumn === 'left') {\n            // Try right column\n            this.currentColumn = 'right';\n            return false; // No new page needed\n        } else {\n            // Need new page\n            return true;\n        }\n    }\n    resetColumnsForNewPage() {\n        this.currentColumn = 'left';\n        this.leftColumnY = this.pageContentStartY;\n        this.rightColumnY = this.pageContentStartY;\n    }\n    async checkColumnPageBreak(estimatedHeight, collegeInfo, subjectName) {\n        const currentY = this.getCurrentColumnY();\n        if (currentY + estimatedHeight > this.pageHeight - this.margin) {\n            if (this.moveToNextAvailablePosition()) {\n                await this.addNewPage(collegeInfo, subjectName);\n                return true;\n            }\n            this.updateCurrentColumnY(this.pageContentStartY);\n        }\n        return false;\n    }\n    addColumnSeparator() {\n        const startX = this.margin + this.columnWidth + this.columnGap / 2;\n        this.doc.setDrawColor(150, 150, 150); // Light grey color\n        this.doc.line(startX, this.currentY, startX, this.pageHeight - this.margin);\n    }\n    async addWatermark(collegeInfo) {\n        // Use college logo if available, otherwise use default Medicos logo\n        const logoUrl = collegeInfo.logoUrl || '/assets/logo/medicos-logo.svg';\n        if (!logoUrl) {\n            console.log('No logo URL available for watermark.');\n            return;\n        }\n        try {\n            console.log('Attempting to add watermark...');\n            const imageUrl = logoUrl;\n            console.log('Watermark image URL:', imageUrl);\n            // Handle different image URL formats\n            let imageDataUrl;\n            if (imageUrl.startsWith('data:')) {\n                // Already a data URL\n                imageDataUrl = imageUrl;\n            } else {\n                // Fetch the image and convert to data URL\n                const imageResponse = await fetch(imageUrl, {\n                    mode: 'cors',\n                    credentials: 'omit'\n                });\n                console.log('Fetched watermark image, status:', imageResponse.status);\n                if (!imageResponse.ok) {\n                    throw new Error(`Failed to fetch watermark image from ${imageUrl} with status ${imageResponse.status}`);\n                }\n                const imageBlob = await imageResponse.blob();\n                console.log('Watermark image blob created, type:', imageBlob.type);\n                // Convert blob to data URL\n                imageDataUrl = await new Promise((resolve, reject)=>{\n                    const reader = new FileReader();\n                    reader.onload = ()=>resolve(reader.result);\n                    reader.onerror = reject;\n                    reader.readAsDataURL(imageBlob);\n                });\n            }\n            console.log('Watermark image data URL created.');\n            const totalPages = this.doc.internal.getNumberOfPages();\n            console.log(`Adding watermark to ${totalPages} pages.`);\n            // Determine image format from data URL\n            const imageFormat = imageDataUrl.includes('data:image/png') ? 'PNG' : imageDataUrl.includes('data:image/jpeg') || imageDataUrl.includes('data:image/jpg') ? 'JPEG' : imageDataUrl.includes('data:image/svg') ? 'SVG' : 'PNG';\n            for(let i = 1; i <= totalPages; i++){\n                this.doc.setPage(i);\n                this.doc.saveGraphicsState();\n                this.doc.setGState(new this.doc.GState({\n                    opacity: 0.1\n                }));\n                // Center the watermark on the page\n                const pageWidth = this.doc.internal.pageSize.getWidth();\n                const pageHeight = this.doc.internal.pageSize.getHeight();\n                const watermarkSize = 110;\n                const x = (pageWidth - watermarkSize) / 2;\n                const y = (pageHeight - watermarkSize) / 2;\n                this.doc.addImage(imageDataUrl, imageFormat, x, y, watermarkSize, watermarkSize);\n                this.doc.restoreGraphicsState();\n            }\n            console.log('Watermark added successfully.');\n        } catch (error) {\n            console.error('Failed to add watermark image: ', error);\n            // Try to add a fallback text watermark\n            try {\n                const totalPages = this.doc.internal.getNumberOfPages();\n                for(let i = 1; i <= totalPages; i++){\n                    this.doc.setPage(i);\n                    this.doc.saveGraphicsState();\n                    this.doc.setGState(new this.doc.GState({\n                        opacity: 0.1\n                    }));\n                    this.doc.setFontSize(48);\n                    this.doc.setTextColor(200, 200, 200);\n                    const pageWidth = this.doc.internal.pageSize.getWidth();\n                    const pageHeight = this.doc.internal.pageSize.getHeight();\n                    this.doc.text('MEDICOS', pageWidth / 2, pageHeight / 2, {\n                        align: 'center',\n                        angle: 45\n                    });\n                    this.doc.restoreGraphicsState();\n                }\n                console.log('Fallback text watermark added.');\n            } catch (fallbackError) {\n                console.error('Failed to add fallback watermark:', fallbackError);\n            }\n        }\n    }\n    async addHeader(collegeInfo, questionPaper) {\n        const headerY = this.margin;\n        const leftSectionX = this.margin;\n        const rightSectionX = this.pageWidth - this.margin;\n        const centerSectionX = this.pageWidth / 2;\n        // Define thirds of the page for flexible sections\n        const sectionWidth = (this.pageWidth - this.margin * 2) / 3;\n        let leftHeight = 0;\n        let centerHeight = 0;\n        let rightHeight = 0;\n        // Left Section: College Info & Logo\n        if (collegeInfo) {\n            let currentLeftX = leftSectionX;\n            if (collegeInfo.logoUrl) {\n                try {\n                    const dataUrl = await (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_1__.ensureDataUrl)(collegeInfo.logoUrl);\n                    const img = new Image();\n                    img.src = dataUrl;\n                    await new Promise((resolve, reject)=>{\n                        img.onload = ()=>resolve();\n                        img.onerror = reject;\n                    });\n                    const logoHeight = 10;\n                    const logoWidth = img.width * logoHeight / img.height;\n                    if (logoWidth > 0 && logoHeight > 0) {\n                        this.doc.addImage(dataUrl, 'PNG', currentLeftX, headerY, logoWidth, logoHeight);\n                        currentLeftX += logoWidth + 2; // 2mm gap\n                        leftHeight = Math.max(leftHeight, logoHeight);\n                    }\n                } catch (e) {\n                    console.error(\"Failed to load college logo\", e);\n                }\n            }\n            this.doc.setFont('helvetica', 'bold');\n            this.doc.setFontSize(this.FONT_SIZES.COLLEGE_NAME);\n            const collegeNameLines = this.doc.splitTextToSize(collegeInfo.name, sectionWidth - (currentLeftX - leftSectionX));\n            this.doc.text(collegeNameLines, currentLeftX, headerY + 4); // Vertically align\n            leftHeight = Math.max(leftHeight, collegeNameLines.length * 4);\n        }\n        // Center Section: Title\n        this.doc.setFont('helvetica', 'bold');\n        this.doc.setFontSize(this.FONT_SIZES.TITLE);\n        const titleLines = this.doc.splitTextToSize(questionPaper.title, sectionWidth + 10);\n        this.doc.text(titleLines, centerSectionX, headerY + 4, {\n            align: 'center'\n        });\n        centerHeight = titleLines.length * 5;\n        // Right Section: Exam Details\n        this.doc.setFont('helvetica', 'normal');\n        this.doc.setFontSize(this.FONT_SIZES.EXAM_DETAILS);\n        const durationText = `Duration: ${questionPaper.duration} mins`;\n        const marksText = `Total Marks: ${questionPaper.totalMarks}`;\n        this.doc.text(durationText, rightSectionX, headerY + 2, {\n            align: 'right'\n        });\n        this.doc.text(marksText, rightSectionX, headerY + 2 + 5, {\n            align: 'right'\n        });\n        rightHeight = 12; // Approx height for two lines\n        // Set currentY to be below the tallest section\n        this.currentY = headerY + Math.max(leftHeight, centerHeight, rightHeight) + 3;\n        // Add a professional line separator\n        this.doc.setDrawColor(0);\n        this.doc.setLineWidth(0.5);\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 5;\n        this.doc.setLineWidth(0.2); // Reset line width\n        this.pageContentStartY = this.currentY;\n    }\n    checkPageBreak(requiredHeight) {\n        if (this.currentY + requiredHeight > this.pageHeight - this.margin) {\n            this.addNewPage();\n            return true;\n        }\n        return false;\n    }\n    async addNewPage(collegeInfo, subjectName) {\n        this.addInstitutionFooter(); // Add footer to the completed page\n        this.doc.addPage();\n        this.resetColumnsForNewPage();\n        this.currentY = this.margin;\n        // Main header is not repeated on subsequent pages.\n        // A running header could be added here if needed.\n        if (subjectName) {\n            this.addSubjectHeader(subjectName);\n        }\n        this.addColumnSeparator(); // Add separator on new page\n    }\n    addSubjectHeader(subjectName) {\n        this.doc.setFontSize(this.FONT_SIZES.PART_HEADER);\n        this.doc.setFont('helvetica', 'bold');\n        // Add subject label on the left side\n        const subjectText = `Subject: ${subjectName}`;\n        this.doc.text(subjectText, this.margin, this.currentY);\n        this.currentY += 12; // More spacing after subject header\n    }\n    addPartHeader(partLetter, subjectName) {\n        this.doc.setFontSize(this.FONT_SIZES.PART_HEADER);\n        this.doc.setFont('helvetica', 'bold');\n        const headerText = `PART - ${partLetter} (${subjectName.toUpperCase()})`;\n        this.doc.text(headerText, this.margin, this.currentY);\n        this.currentY += 8;\n        this.doc.setDrawColor(0);\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 5;\n    }\n    addSectionHeader(sectionName, questionCount) {\n        this.doc.setFontSize(this.FONT_SIZES.SECTION_HEADER);\n        this.doc.setFont('helvetica', 'bold');\n        const headerText = `${sectionName} (Questions ${questionCount})`;\n        this.doc.text(headerText, this.margin, this.currentY);\n        this.currentY += 6;\n    }\n    addMarkingScheme() {\n        this.doc.setFontSize(this.FONT_SIZES.MARKING_SCHEME);\n        this.doc.setFont('helvetica', 'italic');\n        const schemeText = 'Marking Scheme: +4 for correct, -1 for incorrect, 0 for unattempted.';\n        this.doc.text(schemeText, this.pageWidth - this.margin, this.currentY, {\n            align: 'right'\n        });\n        this.currentY += 8;\n    }\n    /**\n   * Render LaTeX math expression as SVG image for PDF embedding\n   */ async renderMathAsImage(expression, isBlock = false, currentX, currentY, maxWidth) {\n        console.log('Rendering math expression as SVG image:', expression);\n        try {\n            // Use SVG renderer for lightweight, production-ready math rendering\n            const svgResult = await _utils_svgMathRenderer__WEBPACK_IMPORTED_MODULE_2__.SVGMathRenderer.renderMathToSVG(expression, isBlock, {\n                fontSize: isBlock ? 18 : 16,\n                backgroundColor: 'transparent',\n                padding: 2,\n                maxWidth: maxWidth\n            });\n            if (svgResult.success && svgResult.dataUrl) {\n                // Calculate scaling to fit within maxWidth\n                let imageWidth = svgResult.width;\n                let imageHeight = svgResult.height;\n                if (imageWidth > maxWidth) {\n                    const scale = maxWidth / imageWidth;\n                    imageWidth = maxWidth;\n                    imageHeight = imageHeight * scale;\n                }\n                // Calculate proper Y position for image (align with text baseline)\n                const imageY = currentY - imageHeight * 0.7; // Better baseline alignment\n                // Add SVG image to PDF\n                this.doc.addImage(svgResult.dataUrl, 'SVG', currentX, imageY, imageWidth, imageHeight);\n                console.log('Successfully added math SVG to PDF');\n                return {\n                    success: true,\n                    width: imageWidth,\n                    height: imageHeight,\n                    newX: currentX + imageWidth + 2,\n                    newY: currentY // Keep same Y for inline math\n                };\n            }\n            // Fallback to text rendering\n            console.log('SVG rendering failed, falling back to text');\n            return this.renderMathAsText(expression, isBlock, currentX, currentY, maxWidth);\n        } catch (error) {\n            console.error('Math SVG rendering error:', error);\n            // Fallback to text rendering\n            return this.renderMathAsText(expression, isBlock, currentX, currentY, maxWidth);\n        }\n    }\n    /**\n   * Fallback method to render math as Unicode text\n   */ async renderMathAsText(expression, isBlock, currentX, currentY, maxWidth) {\n        const mathText = this.convertMathToUnicode(expression);\n        const textWidth = this.doc.getTextWidth(mathText);\n        // Check if text fits\n        if (textWidth <= maxWidth) {\n            this.doc.text(mathText, currentX, currentY);\n            return {\n                success: true,\n                width: textWidth,\n                height: 16,\n                newX: currentX + textWidth,\n                newY: currentY\n            };\n        } else {\n            // Text too wide, might need wrapping or scaling\n            const scale = maxWidth / textWidth;\n            this.doc.setFontSize(this.doc.getFontSize() * scale);\n            this.doc.text(mathText, currentX, currentY);\n            this.doc.setFontSize(this.doc.getFontSize() / scale); // Reset font size\n            return {\n                success: true,\n                width: maxWidth,\n                height: 16 * scale,\n                newX: currentX + maxWidth,\n                newY: currentY\n            };\n        }\n    }\n    /**\n   * Legacy Unicode converter as fallback\n   */ convertMathToUnicode(text) {\n        console.log('Using Unicode fallback for:', text);\n        let displayText = text// Arrows and implications\n        .replace(/\\\\Rightarrow/g, '⇒').replace(/\\\\Leftarrow/g, '⇐').replace(/\\\\Leftrightarrow/g, '⇔').replace(/\\\\rightarrow/g, '→').replace(/\\\\leftarrow/g, '←').replace(/\\\\leftrightarrow/g, '↔').replace(/\\\\uparrow/g, '↑').replace(/\\\\downarrow/g, '↓').replace(/\\\\updownarrow/g, '↕').replace(/\\\\Uparrow/g, '⇑').replace(/\\\\Downarrow/g, '⇓').replace(/\\\\Updownarrow/g, '⇕')// Comparison operators\n        .replace(/\\\\geq/g, '≥').replace(/\\\\leq/g, '≤').replace(/\\\\neq/g, '≠').replace(/\\\\approx/g, '≈').replace(/\\\\equiv/g, '≡').replace(/\\\\sim/g, '∼').replace(/\\\\simeq/g, '≃').replace(/\\\\cong/g, '≅').replace(/\\\\propto/g, '∝')// Binary operators\n        .replace(/\\\\pm/g, '±').replace(/\\\\mp/g, '∓').replace(/\\\\times/g, '×').replace(/\\\\div/g, '÷').replace(/\\\\cdot/g, '⋅').replace(/\\\\ast/g, '∗').replace(/\\\\star/g, '⋆').replace(/\\\\circ/g, '∘').replace(/\\\\bullet/g, '•').replace(/\\\\oplus/g, '⊕').replace(/\\\\ominus/g, '⊖').replace(/\\\\otimes/g, '⊗').replace(/\\\\oslash/g, '⊘')// Greek letters (lowercase)\n        .replace(/\\\\alpha/g, 'α').replace(/\\\\beta/g, 'β').replace(/\\\\gamma/g, 'γ').replace(/\\\\delta/g, 'δ').replace(/\\\\epsilon/g, 'ε').replace(/\\\\varepsilon/g, 'ε').replace(/\\\\zeta/g, 'ζ').replace(/\\\\eta/g, 'η').replace(/\\\\theta/g, 'θ').replace(/\\\\vartheta/g, 'ϑ').replace(/\\\\iota/g, 'ι').replace(/\\\\kappa/g, 'κ').replace(/\\\\lambda/g, 'λ').replace(/\\\\mu/g, 'μ').replace(/\\\\nu/g, 'ν').replace(/\\\\xi/g, 'ξ').replace(/\\\\pi/g, 'π').replace(/\\\\varpi/g, 'ϖ').replace(/\\\\rho/g, 'ρ').replace(/\\\\varrho/g, 'ϱ').replace(/\\\\sigma/g, 'σ').replace(/\\\\varsigma/g, 'ς').replace(/\\\\tau/g, 'τ').replace(/\\\\upsilon/g, 'υ').replace(/\\\\phi/g, 'φ').replace(/\\\\varphi/g, 'φ').replace(/\\\\chi/g, 'χ').replace(/\\\\psi/g, 'ψ').replace(/\\\\omega/g, 'ω')// Greek letters (uppercase)\n        .replace(/\\\\Gamma/g, 'Γ').replace(/\\\\Delta/g, 'Δ').replace(/\\\\Theta/g, 'Θ').replace(/\\\\Lambda/g, 'Λ').replace(/\\\\Xi/g, 'Ξ').replace(/\\\\Pi/g, 'Π').replace(/\\\\Sigma/g, 'Σ').replace(/\\\\Upsilon/g, 'Υ').replace(/\\\\Phi/g, 'Φ').replace(/\\\\Psi/g, 'Ψ').replace(/\\\\Omega/g, 'Ω')// Large operators\n        .replace(/\\\\sum/g, '∑').replace(/\\\\prod/g, '∏').replace(/\\\\coprod/g, '∐').replace(/\\\\int/g, '∫').replace(/\\\\iint/g, '∬').replace(/\\\\iiint/g, '∭').replace(/\\\\oint/g, '∮').replace(/\\\\bigcup/g, '⋃').replace(/\\\\bigcap/g, '⋂').replace(/\\\\bigoplus/g, '⨁').replace(/\\\\bigotimes/g, '⨂')// Set theory and logic\n        .replace(/\\\\in/g, '∈').replace(/\\\\notin/g, '∉').replace(/\\\\ni/g, '∋').replace(/\\\\subset/g, '⊂').replace(/\\\\supset/g, '⊃').replace(/\\\\subseteq/g, '⊆').replace(/\\\\supseteq/g, '⊇').replace(/\\\\cup/g, '∪').replace(/\\\\cap/g, '∩').replace(/\\\\setminus/g, '∖').replace(/\\\\forall/g, '∀').replace(/\\\\exists/g, '∃').replace(/\\\\nexists/g, '∄').replace(/\\\\emptyset/g, '∅').replace(/\\\\varnothing/g, '∅')// Special symbols\n        .replace(/\\\\infty/g, '∞').replace(/\\\\partial/g, '∂').replace(/\\\\nabla/g, '∇').replace(/\\\\angle/g, '∠').replace(/\\\\triangle/g, '△').replace(/\\\\square/g, '□').replace(/\\\\diamond/g, '◊').replace(/\\\\clubsuit/g, '♣').replace(/\\\\diamondsuit/g, '♦').replace(/\\\\heartsuit/g, '♥').replace(/\\\\spadesuit/g, '♠')// Number sets\n        .replace(/\\\\mathbb{N}/g, 'ℕ').replace(/\\\\mathbb{Z}/g, 'ℤ').replace(/\\\\mathbb{Q}/g, 'ℚ').replace(/\\\\mathbb{R}/g, 'ℝ').replace(/\\\\mathbb{C}/g, 'ℂ').replace(/\\\\mathbb{P}/g, 'ℙ')// Functions and operations\n        .replace(/\\\\sqrt{([^}]+)}/g, '√($1)').replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)').replace(/\\\\binom{([^}]+)}{([^}]+)}/g, 'C($1,$2)').replace(/\\\\choose/g, 'C')// Delimiters - remove \\left and \\right commands\n        .replace(/\\\\left\\(/g, '(').replace(/\\\\right\\)/g, ')').replace(/\\\\left\\[/g, '[').replace(/\\\\right\\]/g, ']').replace(/\\\\left\\{/g, '{').replace(/\\\\right\\}/g, '}').replace(/\\\\left\\|/g, '|').replace(/\\\\right\\|/g, '|').replace(/\\\\left</g, '⟨').replace(/\\\\right>/g, '⟩').replace(/\\\\left/g, '') // Remove any remaining \\left\n        .replace(/\\\\right/g, '') // Remove any remaining \\right\n        // Dots and ellipsis\n        .replace(/\\\\ldots/g, '...').replace(/\\\\cdots/g, '⋯').replace(/\\\\vdots/g, '⋮').replace(/\\\\ddots/g, '⋱')// Superscripts and subscripts\n        .replace(/\\^{([^}]+)}/g, '^($1)').replace(/_{([^}]+)}/g, '_($1)').replace(/\\^(\\w)/g, '^$1').replace(/_(\\w)/g, '_$1')// Text formatting\n        .replace(/\\\\mathrm{([^}]+)}/g, '$1').replace(/\\\\mathbf{([^}]+)}/g, '$1').replace(/\\\\mathit{([^}]+)}/g, '$1').replace(/\\\\mathcal{([^}]+)}/g, '$1').replace(/\\\\text{([^}]+)}/g, '$1').replace(/\\\\textbf{([^}]+)}/g, '$1').replace(/\\\\textit{([^}]+)}/g, '$1')// Clean up any remaining LaTeX commands that might cause issues\n        .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1') // Remove unknown commands with braces\n        .replace(/\\\\[a-zA-Z]+/g, '') // Remove unknown commands without braces\n        // Fix spacing issues - normalize multiple spaces\n        .replace(/\\s+/g, ' ').trim()// Remove delimiters\n        .replace(/\\$([^$]+)\\$/g, '$1') // Remove $ delimiters\n        .replace(/\\$\\$([^$]+)\\$\\$/g, '$1'); // Remove $$ delimiters\n        console.log('Converted to:', displayText);\n        return displayText;\n    }\n    addInstitutionFooter() {\n        const footerY = this.pageHeight - 10;\n        this.doc.setFontSize(this.FONT_SIZES.FOOTER);\n        this.doc.setFont('helvetica', 'italic');\n        this.doc.text(`Generated by Medicos - ${new Date().toLocaleDateString()}`, this.pageWidth / 2, footerY, {\n            align: 'center'\n        });\n    }\n    detectQuestionType(question) {\n        // Basic detection logic, can be expanded\n        if (question.content.toLowerCase().includes('assertion') && question.content.toLowerCase().includes('reason')) {\n            return 'assertion-reason';\n        }\n        if (question.content.toLowerCase().includes('column i') && question.content.toLowerCase().includes('column ii')) {\n            return 'match-columns';\n        }\n        return 'standard';\n    }\n    formatAssertionReasonQuestion(content) {\n        return content; // Placeholder\n    }\n    formatMatchColumnsQuestion(content) {\n        return content; // Placeholder\n    }\n    processTextWithImages(text) {\n        const { cleanText, images } = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_1__.extractImagesFromText)(text);\n        return {\n            cleanText,\n            images\n        };\n    }\n    async addImageToPDF(src, x, y, maxWidth) {\n        if (!(0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_1__.isBase64Image)(src)) {\n            console.error('Image source is not a base64 string.');\n            return 0;\n        }\n        const dataUrl = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_1__.ensureDataUrl)(src);\n        const img = new Image();\n        img.src = dataUrl;\n        await new Promise((resolve)=>img.onload = resolve);\n        const aspectRatio = img.width / img.height;\n        const width = Math.min(maxWidth, img.width);\n        const height = width / aspectRatio;\n        this.doc.addImage(dataUrl, 'JPEG', x, y, width, height);\n        return height;\n    }\n    standardizeOptions(options) {\n        return options.map((opt)=>opt.replace(/\\n/g, ''));\n    }\n    async addRichContent(text, x, maxWidth, collegeInfo, subjectName) {\n        // Simple approach: render text with inline math handling\n        const regex = /(\\$\\$[^$]+\\$\\$|\\$[^$]+?\\$)/g;\n        let lastIndex = 0;\n        let match;\n        let currentX = this.getCurrentColumnX();\n        let currentY = this.getCurrentColumnY();\n        const startX = this.getCurrentColumnX();\n        const lineHeight = 6; // Slightly more space to prevent overlapping\n        const safetyPadding = 3;\n        const advanceLine = async ()=>{\n            this.updateCurrentColumnY(this.getCurrentColumnY() + lineHeight);\n            await this.checkColumnPageBreak(lineHeight, collegeInfo, subjectName);\n            currentY = this.getCurrentColumnY();\n            currentX = this.getCurrentColumnX();\n        };\n        // Process text with math formulas\n        while((match = regex.exec(text)) !== null){\n            // 1. Render text before the formula\n            const beforeText = text.substring(lastIndex, match.index).trim();\n            if (beforeText) {\n                await this.renderSimpleText(beforeText, currentX, currentY, maxWidth, startX, advanceLine);\n                currentX = this.getCurrentColumnX();\n                currentY = this.getCurrentColumnY();\n            }\n            // 2. Render the math formula\n            const formulaText = match[0];\n            const isBlock = formulaText.startsWith('$$');\n            const formula = isBlock ? formulaText.slice(2, -2).trim() : formulaText.slice(1, -1).trim();\n            if (formula) {\n                await this.renderSimpleMath(formula, isBlock, currentX, currentY, maxWidth, startX, advanceLine);\n                currentX = this.getCurrentColumnX();\n                currentY = this.getCurrentColumnY();\n            }\n            lastIndex = regex.lastIndex;\n        }\n        // 3. Render remaining text\n        const remainingText = text.substring(lastIndex).trim();\n        if (remainingText) {\n            await this.renderSimpleText(remainingText, currentX, currentY, maxWidth, startX, advanceLine);\n        }\n        // Move to next line\n        this.updateCurrentColumnY(this.getCurrentColumnY() + lineHeight);\n    }\n    /**\n   * Render simple text with word wrapping\n   */ async renderSimpleText(text, currentX, currentY, maxWidth, startX, advanceLine) {\n        const words = text.split(' ').filter((word)=>word.length > 0);\n        let x = currentX;\n        for (const word of words){\n            const wordWidth = this.doc.getTextWidth(word);\n            const spaceWidth = this.doc.getTextWidth(' ');\n            const needsSpace = x > startX;\n            const totalWidth = wordWidth + (needsSpace ? spaceWidth : 0);\n            // Check if word fits on current line\n            if (x + totalWidth > startX + maxWidth - 5) {\n                await advanceLine();\n                x = this.getCurrentColumnX();\n                currentY = this.getCurrentColumnY();\n            } else if (needsSpace) {\n                this.doc.text(' ', x, currentY);\n                x += spaceWidth;\n            }\n            this.doc.text(word, x, currentY);\n            x += wordWidth;\n        }\n        // Update position\n        this.updateCurrentColumnX(x);\n    }\n    /**\n   * Render simple math with fallback\n   */ async renderSimpleMath(formula, isBlock, currentX, currentY, maxWidth, startX, advanceLine) {\n        try {\n            // Try to render as image first\n            const mathResult = await this.renderMathAsImage(formula, isBlock, currentX, currentY, maxWidth - (currentX - startX));\n            if (mathResult.success) {\n                this.updateCurrentColumnX(mathResult.newX);\n                return;\n            }\n        } catch (error) {\n            console.warn('Math image rendering failed:', error);\n        }\n        // Fallback to Unicode text\n        const mathText = this.convertMathToUnicode(formula);\n        const textWidth = this.doc.getTextWidth(mathText);\n        // Check if math text fits on current line\n        if (currentX + textWidth > startX + maxWidth - 5) {\n            await advanceLine();\n            currentX = this.getCurrentColumnX();\n            currentY = this.getCurrentColumnY();\n        }\n        this.doc.text(mathText, currentX, currentY);\n        this.updateCurrentColumnX(currentX + textWidth);\n    }\n    /**\n   * Update current column X position (helper method)\n   */ updateCurrentColumnX(newX) {\n        // This is a simple helper - in a full implementation you'd track column positions\n        // For now, we'll just ensure the position is valid\n        if (newX >= this.getCurrentColumnX()) {\n        // Position moved forward, which is expected\n        }\n    }\n    async addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName) {\n        const estimatedHeight = this.estimateQuestionHeight(question, withAnswers);\n        await this.checkColumnPageBreak(estimatedHeight, collegeInfo, subjectName);\n        const columnX = this.getCurrentColumnX();\n        // Question\n        this.doc.setFontSize(this.FONT_SIZES.QUESTION_TEXT);\n        this.doc.setFont('helvetica', 'normal');\n        const questionText = `${questionNumber}. ${question.content}`;\n        await this.addRichContent(questionText, columnX, this.columnWidth, collegeInfo, subjectName);\n        // Options\n        if (question.options && question.options.length > 0) {\n            this.updateCurrentColumnY(this.getCurrentColumnY() + 2);\n            this.doc.setFontSize(this.FONT_SIZES.OPTION_TEXT);\n            for(let i = 0; i < question.options.length; i++){\n                const optionText = `(${String.fromCharCode(97 + i)}) ${question.options[i]}`;\n                await this.addRichContent(optionText, columnX, this.columnWidth, collegeInfo, subjectName);\n                this.updateCurrentColumnY(this.getCurrentColumnY() + 1);\n            }\n        }\n        // Answer\n        if (withAnswers && question.answer) {\n            this.updateCurrentColumnY(this.getCurrentColumnY() + 2);\n            this.doc.setFontSize(this.FONT_SIZES.ANSWER_TEXT);\n            this.doc.setFont('helvetica', 'bold');\n            const answerText = `Ans: ${question.answer}`;\n            await this.addRichContent(answerText, columnX, this.columnWidth, collegeInfo, subjectName);\n            this.doc.setFont('helvetica', 'normal');\n        }\n        this.updateCurrentColumnY(this.getCurrentColumnY() + 3);\n    }\n    async addQuestionWithSolutions(question, questionNumber, withAnswers, withSolutions, withHints, collegeInfo, subjectName) {\n        await this.addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName);\n        const columnX = this.getCurrentColumnX();\n        if (withSolutions && question.solution) {\n            this.updateCurrentColumnY(this.getCurrentColumnY() + 2);\n            this.doc.setFontSize(this.FONT_SIZES.SOLUTION_HEADER);\n            this.doc.setFont('helvetica', 'bold');\n            await this.addRichContent(\"Solution:\", columnX, this.columnWidth, collegeInfo, subjectName);\n            this.doc.setFontSize(this.FONT_SIZES.SOLUTION_TEXT);\n            this.doc.setFont('helvetica', 'normal');\n            const solutionText = question.solution.final_explanation || (question.solution.steps || []).join('\\n');\n            await this.addRichContent(solutionText, columnX, this.columnWidth, collegeInfo, subjectName);\n        }\n        if (withHints && question.hints && question.hints.length > 0) {\n            this.updateCurrentColumnY(this.getCurrentColumnY() + 2);\n            this.doc.setFontSize(this.FONT_SIZES.SOLUTION_HEADER);\n            this.doc.setFont('helvetica', 'bold');\n            await this.addRichContent(\"Hints:\", columnX, this.columnWidth, collegeInfo, subjectName);\n            this.doc.setFontSize(this.FONT_SIZES.HINT_TEXT);\n            this.doc.setFont('helvetica', 'normal');\n            const hintsText = question.hints.map((h)=>`• ${h}`).join('\\n');\n            await this.addRichContent(hintsText, columnX, this.columnWidth, collegeInfo, subjectName);\n        }\n    }\n    estimateQuestionHeight(question, withAnswers) {\n        let height = 0;\n        const tempDoc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"](); // Use a temporary doc for text measurement\n        tempDoc.setFontSize(this.FONT_SIZES.QUESTION_TEXT);\n        const questionLines = tempDoc.splitTextToSize(question.content, this.columnWidth);\n        height += questionLines.length * 4;\n        if (question.options && question.options.length > 0) {\n            height += 20; // Approximation for 4 options in 2x2 grid\n        }\n        if (withAnswers && question.answer) {\n            height += 6;\n        }\n        // Add buffer\n        height += 10;\n        return height;\n    }\n    estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints) {\n        let height = this.estimateQuestionHeight(question, withAnswers);\n        if (withSolutions && question.solution) {\n            height += 40; // Rough estimate for solution block\n        }\n        if (withHints && question.hints && question.hints.length > 0) {\n            height += 20; // Rough estimate for hints block\n        }\n        return height;\n    }\n    async generatePDF(questionPaper, collegeInfo) {\n        if (questionPaper.isMultiSubject) {\n            await this.generateMultiSubjectPDF(questionPaper, collegeInfo);\n        } else {\n            await this.generateSingleSubjectPDF(questionPaper, collegeInfo);\n        }\n        // Finalize: Add watermark and return blob\n        // Always try to add watermark (will use default Medicos logo if no college logo)\n        await this.addWatermark(collegeInfo || {});\n        return this.doc.output('blob');\n    }\n    async generateSingleSubjectPDF(questionPaper, collegeInfo) {\n        // First page setup\n        await this.addHeader(collegeInfo, questionPaper);\n        // ... (rest of the code remains the same)\n        // Add subject header for single-subject papers\n        if (questionPaper.subjectId?.name) {\n            this.addSubjectHeader(questionPaper.subjectId.name);\n        }\n        // Initialize column layout\n        this.resetColumnsForNewPage();\n        this.leftColumnY = this.currentY;\n        this.rightColumnY = this.currentY;\n        this.addColumnSeparator();\n        for(let index = 0; index < questionPaper.questions.length; index++){\n            const question = questionPaper.questions[index];\n            try {\n                if (questionPaper.withSolutions || questionPaper.withHints) {\n                    await this.addQuestionWithSolutions(question, index + 1, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo);\n                } else {\n                    await this.addQuestion(question, index + 1, questionPaper.withAnswers, collegeInfo);\n                }\n            } catch (error) {\n                console.error(`Error processing question ${index + 1}:`, error);\n            }\n        }\n        this.addInstitutionFooter();\n    }\n    async generateMultiSubjectPDF(questionPaper, collegeInfo) {\n        if (!questionPaper.sections) return;\n        let overallQuestionNumber = 1;\n        // First page setup\n        await this.addHeader(collegeInfo, questionPaper);\n        this.resetColumnsForNewPage();\n        this.leftColumnY = this.currentY;\n        this.rightColumnY = this.currentY;\n        for(let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++){\n            const section = questionPaper.sections[sectionIndex];\n            const subjectName = section.subjectName || section.name || `Subject ${sectionIndex + 1}`;\n            const partLetter = String.fromCharCode(65 + sectionIndex);\n            if (sectionIndex > 0) {\n                await this.addNewPage(collegeInfo, subjectName);\n            }\n            this.addPartHeader(partLetter, subjectName);\n            this.addSectionHeader('Section - I: Single Correct', section.questions?.length || 0);\n            this.addColumnSeparator();\n            if (section.questions && section.questions.length > 0) {\n                for (const questionItem of section.questions){\n                    const question = questionItem.question;\n                    try {\n                        if (questionPaper.withSolutions || questionPaper.withHints) {\n                            await this.addQuestionWithSolutions(question, overallQuestionNumber, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo, subjectName);\n                        } else {\n                            await this.addQuestion(question, overallQuestionNumber, questionPaper.withAnswers, collegeInfo, subjectName);\n                        }\n                        overallQuestionNumber++;\n                    } catch (error) {\n                        console.error(`Error processing question ${overallQuestionNumber}:`, error);\n                        overallQuestionNumber++;\n                    }\n                }\n            }\n        }\n        this.addInstitutionFooter();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PDFGenerator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/pdfGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/svgMathRenderer.ts":
/*!**************************************!*\
  !*** ./src/utils/svgMathRenderer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SVGMathRenderer: () => (/* binding */ SVGMathRenderer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _mathRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mathRenderer */ \"(ssr)/./src/utils/mathRenderer.ts\");\n/**\n * Lightweight SVG-based Math Renderer for PDF Generation\n * \n * This utility provides a production-ready solution for rendering mathematical\n * expressions as SVG images that can be embedded in PDFs without heavy dependencies.\n */ \nclass SVGMathRenderer {\n    static{\n        this.DEFAULT_OPTIONS = {\n            fontSize: 16,\n            fontFamily: 'Times New Roman, serif',\n            color: '#000000',\n            backgroundColor: 'transparent',\n            padding: 4,\n            maxWidth: 400\n        };\n    }\n    /**\n   * Render LaTeX expression as SVG\n   */ static async renderMathToSVG(expression, isBlock = false, options = {}) {\n        const mergedOptions = {\n            ...this.DEFAULT_OPTIONS,\n            ...options\n        };\n        try {\n            console.log('Rendering math to SVG:', expression);\n            // First try KaTeX rendering to get proper fallback text\n            const katexResult = _mathRenderer__WEBPACK_IMPORTED_MODULE_0__.MathRenderer.renderToHTML(expression, {\n                displayMode: isBlock,\n                throwOnError: false,\n                strict: false\n            });\n            // Use fallback text for SVG rendering\n            const mathText = katexResult.fallbackText || expression;\n            // Calculate dimensions more accurately\n            const fontSize = isBlock ? mergedOptions.fontSize * 1.3 : mergedOptions.fontSize;\n            const textLength = mathText.length;\n            const charWidth = fontSize * 0.55; // More accurate character width\n            const textWidth = Math.min(textLength * charWidth, mergedOptions.maxWidth);\n            const textHeight = fontSize * 1.1; // Tighter height to prevent overlapping\n            const totalWidth = textWidth + mergedOptions.padding * 2;\n            const totalHeight = textHeight + mergedOptions.padding * 2;\n            // Create SVG\n            const svg = this.createMathSVG(mathText, totalWidth, totalHeight, fontSize, mergedOptions);\n            // Convert to data URL\n            const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;\n            console.log('Successfully rendered math to SVG');\n            return {\n                svg,\n                dataUrl,\n                width: totalWidth,\n                height: totalHeight,\n                success: true\n            };\n        } catch (error) {\n            console.error('SVG math rendering failed:', error);\n            // Create error placeholder\n            const errorSvg = this.createErrorSVG(expression, mergedOptions);\n            const errorDataUrl = `data:image/svg+xml;base64,${Buffer.from(errorSvg).toString('base64')}`;\n            return {\n                svg: errorSvg,\n                dataUrl: errorDataUrl,\n                width: 100,\n                height: 30,\n                success: false,\n                error: error instanceof Error ? error.message : String(error)\n            };\n        }\n    }\n    /**\n   * Create SVG for mathematical expression\n   */ static createMathSVG(mathText, width, height, fontSize, options) {\n        const centerX = width / 2;\n        const baselineY = height * 0.75; // Better baseline positioning to prevent overlap\n        return `\n      <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\n        <defs>\n          <style>\n            .math-text {\n              font-family: ${options.fontFamily};\n              font-size: ${fontSize}px;\n              fill: ${options.color};\n              text-anchor: middle;\n              dominant-baseline: alphabetic;\n            }\n          </style>\n        </defs>\n        ${options.backgroundColor !== 'transparent' ? `<rect width=\"100%\" height=\"100%\" fill=\"${options.backgroundColor}\"/>` : ''}\n        <text x=\"${centerX}\" y=\"${baselineY}\" class=\"math-text\">${this.escapeXML(mathText)}</text>\n      </svg>\n    `.trim();\n    }\n    /**\n   * Create error placeholder SVG\n   */ static createErrorSVG(expression, options) {\n        return `\n      <svg width=\"100\" height=\"30\" xmlns=\"http://www.w3.org/2000/svg\">\n        <rect width=\"100%\" height=\"100%\" fill=\"#f8f8f8\" stroke=\"#ddd\" stroke-width=\"1\"/>\n        <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dominant-baseline=\"middle\" \n              font-family=\"monospace\" font-size=\"10\" fill=\"#666\">\n          Math Error\n        </text>\n      </svg>\n    `.trim();\n    }\n    /**\n   * Escape XML special characters\n   */ static escapeXML(text) {\n        return text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;');\n    }\n    /**\n   * Batch render multiple expressions\n   */ static async renderMultipleMath(expressions, options = {}) {\n        const results = new Map();\n        for (const expr of expressions){\n            try {\n                const result = await this.renderMathToSVG(expr.expression, expr.isBlock, options);\n                results.set(expr.id, result);\n            } catch (error) {\n                console.error(`Failed to render math expression ${expr.id}:`, error);\n                const errorResult = {\n                    svg: this.createErrorSVG(expr.expression, options),\n                    dataUrl: '',\n                    width: 100,\n                    height: 30,\n                    success: false,\n                    error: error instanceof Error ? error.message : String(error)\n                };\n                results.set(expr.id, errorResult);\n            }\n        }\n        return results;\n    }\n    /**\n   * Estimate SVG dimensions without full rendering\n   */ static estimateDimensions(expression, isBlock = false, options = {}) {\n        const mergedOptions = {\n            ...this.DEFAULT_OPTIONS,\n            ...options\n        };\n        // Get fallback text for estimation\n        const katexResult = _mathRenderer__WEBPACK_IMPORTED_MODULE_0__.MathRenderer.renderToHTML(expression, {\n            displayMode: isBlock,\n            throwOnError: false\n        });\n        const mathText = katexResult.fallbackText || expression;\n        const fontSize = isBlock ? mergedOptions.fontSize * 1.2 : mergedOptions.fontSize;\n        const charWidth = fontSize * 0.6;\n        const textWidth = Math.min(mathText.length * charWidth, mergedOptions.maxWidth);\n        const textHeight = fontSize * 1.2;\n        return {\n            width: textWidth + mergedOptions.padding * 2,\n            height: textHeight + mergedOptions.padding * 2\n        };\n    }\n    /**\n   * Check if expression is complex (needs special handling)\n   */ static isComplexExpression(expression) {\n        const complexPatterns = [\n            /\\\\frac/,\n            /\\\\sqrt/,\n            /\\\\sum/,\n            /\\\\int/,\n            /\\\\prod/,\n            /\\\\matrix/,\n            /\\\\begin/,\n            /\\^{[^}]{2,}}/,\n            /_{[^}]{2,}}/\n        ];\n        return complexPatterns.some((pattern)=>pattern.test(expression));\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SVGMathRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/svgMathRenderer.ts\n");

/***/ })

};
;