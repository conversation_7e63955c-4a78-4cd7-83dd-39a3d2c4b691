"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n * Also handles cases where the data URL prefix is duplicated\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // Handle duplicated data URL prefixes (e.g., \"data:image/jpeg;base64,data:image/jpeg;base64,...\")\n    const duplicatedPrefixPattern = /^(data:image\\/[^;]+;base64,)(data:image\\/[^;]+;base64,)/;\n    if (duplicatedPrefixPattern.test(base64String)) {\n        // Remove the first occurrence of the duplicated prefix\n        base64String = base64String.replace(duplicatedPrefixPattern, '$2');\n    }\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to PNG if we can't determine the format\n    return \"data:image/png;base64,\".concat(base64String);\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // First, look for HTML img tags: <img src=\"data:image/type;base64,...\" alt=\"...\" />\n    const htmlImagePattern = /<img\\s+[^>]*src=[\"']([^\"']+)[\"'][^>]*(?:alt=[\"']([^\"']*)[\"'])?[^>]*\\/?>/gi;\n    const htmlMatches = [\n        ...text.matchAll(htmlImagePattern)\n    ];\n    if (htmlMatches.length > 0) {\n        htmlMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            let imageSrc = match[1];\n            const altText = match[2] || \"Image \".concat(images.length + 1);\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = \"extracted-image-html-\".concat(index);\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the HTML img tag from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for markdown-style image syntax: ![alt](data:image/type;base64,...)\n    const markdownImagePattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n    const markdownMatches = [\n        ...cleanText.matchAll(markdownImagePattern)\n    ]; // Use cleanText to avoid already processed HTML images\n    if (markdownMatches.length > 0) {\n        markdownMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            const altText = match[1] || \"Image \".concat(images.length + 1);\n            let imageSrc = match[2];\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = \"extracted-image-markdown-\".concat(index);\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the markdown image syntax from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for complete data URLs (these take priority over raw base64)\n    const dataUrlPattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;\n    const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images\n    if (dataUrlMatches) {\n        dataUrlMatches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-dataurl-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the complete data URL from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Finally, look for raw base64 strings (only if they're not part of already processed content)\n    const rawBase64Pattern = /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g;\n    const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content\n    if (rawBase64Matches) {\n        rawBase64Matches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-raw-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the raw base64 string from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageUtils.ts\n"));

/***/ })

});