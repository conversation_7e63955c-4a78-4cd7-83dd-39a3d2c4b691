#!/usr/bin/env python3
"""
Test script for explicit question extraction from your PDF format.

This tests the new extraction method that handles:
1. Explicit numbered questions (1., 2., 3., etc.)
2. Multiple choice options (a), b), c), d))
3. Answer key mapping
4. Solution integration
"""

import sys
import json
from question_extractor import QuestionExtractor

def test_explicit_extraction():
    """Test the explicit question extraction with sample text"""
    
    # Sample text that matches your PDF format
    sample_text = """
Session : 2024-25                                    AS PER NEW NTA SYLLABUS
Total Questions : 395

JEE/NEET PHYSICS
7. ALTERNATING CURRENT

Single Correct Answer Type

1. A resistor 30 Ω, inductor of reactance 10 Ω and capacitor of reactance 10 Ω are connected in series to an AC voltage source e = 300√2 sin(ωt). The current in the circuit is
   a) 10√2 A    b) 10 A    c) 30√11 A    d) 30/√11 A

2. The natural frequency (ω₀) of oscillations in L - C circuit is given by
   a) 1/(2π√LC)    b) 1/(2π√LC)    c) 1/√LC    d) √LC

3. An AC source of angular frequency ω is fed across a resistor R and a capacitor C in series. The current registered is I. If the frequency of source is changed to ω/3 (maintaining the same voltage), the current in the circuit is found to be halved. Calculate the ratio of reactance to resistance at the original frequency ω
   a) √3/5    b) 2/√5    c) 1/5    d) 4/5

: ANSWER KEY :
1) b    2) a    3) a    4) d    5) a    6) b    7) a    8) b

: HINTS AND SOLUTIONS :
1 (b)
e = 300√2 sin ωt
I₀ = e₀/Z = 300√2/√(30)² + (10 - 10)²
I₀ = 300√2/30 = 10√2 A

2 (a)
For LC circuit: ω₀ = 1/√LC
Natural frequency = ω₀/2π = 1/(2π√LC)

3 (a)
Original: I = V/√(R² + (1/ωC)²)
New: I/2 = V/√(R² + (3/ωC)²)
Solving: XC/R = √3/5
"""
    
    print("🧪 Testing explicit question extraction...")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Test the explicit question extraction method
        print("🔄 [EXTRACTION] Testing _extract_explicit_questions...")
        questions_data = extractor._extract_explicit_questions(sample_text)
        
        print(f"📊 [QUESTIONS] Extracted {len(questions_data)} questions")
        
        for i, q in enumerate(questions_data[:3]):  # Show first 3
            print(f"\n   Question {q['number']}:")
            print(f"   Text: {q['question'][:100]}...")
            print(f"   Options: {list(q['options'].keys())}")
            print(f"   Option A: {q['options']['A']}")
            print(f"   Option B: {q['options']['B']}")
        
        # Test answer key extraction
        print(f"\n🔄 [ANSWERS] Testing _extract_answer_key_mapping...")
        answer_mapping = extractor._extract_answer_key_mapping(sample_text)
        
        print(f"📊 [ANSWERS] Extracted {len(answer_mapping)} answers")
        print(f"   Sample answers: {dict(list(answer_mapping.items())[:5])}")
        
        # Test solution extraction
        print(f"\n🔄 [SOLUTIONS] Testing _extract_solutions_mapping...")
        solutions_mapping = extractor._extract_solutions_mapping(sample_text)
        
        print(f"📊 [SOLUTIONS] Extracted {len(solutions_mapping)} solutions")
        for i in range(1, min(4, len(solutions_mapping) + 1)):
            if i in solutions_mapping:
                sol = solutions_mapping[i]
                print(f"   Q{i}: {sol['methodology']} - {', '.join(sol['key_concepts'][:2])}")
        
        # Test full combination
        print(f"\n🔄 [COMBINE] Testing _combine_questions_with_answers...")
        final_questions = extractor._combine_questions_with_answers(
            questions_data, answer_mapping, solutions_mapping
        )
        
        print(f"📊 [FINAL] Combined {len(final_questions)} complete questions")
        
        # Show sample final question
        if final_questions:
            sample_q = final_questions[0]
            print(f"\n📝 [SAMPLE] First complete question:")
            print(f"   Question: {sample_q['question'][:100]}...")
            print(f"   Answer: {sample_q['answer']}")
            print(f"   Has solution: {'solution' in sample_q}")
            print(f"   Has hints: {'hints' in sample_q}")
            
            if 'solution' in sample_q:
                print(f"   Solution method: {sample_q['solution']['methodology']}")
        
        return len(final_questions)
        
    except Exception as e:
        print(f"❌ [ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

def test_with_actual_pdf(pdf_path):
    """Test with actual PDF file"""
    
    print(f"\n🧪 Testing with actual PDF: {pdf_path}")
    
    try:
        extractor = QuestionExtractor(ai_provider='gemini')
        
        # Extract using the new structured method
        print("🔄 [EXTRACTION] Running structured extraction...")
        result = extractor.extract_structured_questions_from_pdf(pdf_path)
        
        # Parse results
        questions = json.loads(result)
        
        print(f"📊 [RESULTS] Extracted {len(questions)} questions")
        
        # Analyze results
        questions_with_answers = sum(1 for q in questions if q.get('answer'))
        questions_with_solutions = sum(1 for q in questions if q.get('solution'))
        questions_with_hints = sum(1 for q in questions if q.get('hints'))
        
        print(f"   Questions with answers: {questions_with_answers}")
        print(f"   Questions with solutions: {questions_with_solutions}")
        print(f"   Questions with hints: {questions_with_hints}")
        
        # Show first question
        if questions:
            q = questions[0]
            print(f"\n📝 [SAMPLE] First question:")
            print(f"   Q: {q.get('question', 'No question')[:150]}...")
            print(f"   Options: {list(q.get('options', {}).keys())}")
            print(f"   Answer: {q.get('answer', 'No answer')}")
            if q.get('solution'):
                print(f"   Solution: {q['solution'].get('methodology', 'N/A')}")
        
        return len(questions)
        
    except Exception as e:
        print(f"❌ [ERROR] PDF test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """Main function"""
    
    print("🚀 Explicit Question Extraction Test")
    print("=" * 50)
    
    # Test 1: Sample text extraction
    print("📋 Test 1: Sample text extraction")
    sample_count = test_explicit_extraction()
    
    if sample_count > 0:
        print(f"✅ Sample test passed: {sample_count} questions extracted")
    else:
        print("❌ Sample test failed")
        return
    
    # Test 2: Actual PDF if provided
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        print(f"\n📋 Test 2: Actual PDF extraction")
        pdf_count = test_with_actual_pdf(pdf_path)
        
        if pdf_count > 0:
            print(f"✅ PDF test passed: {pdf_count} questions extracted")
        else:
            print("❌ PDF test failed")
    else:
        print(f"\n💡 To test with actual PDF, run:")
        print(f"   python test_explicit_questions.py your_file.pdf")
    
    print(f"\n🎯 Summary:")
    print(f"   Sample extraction: {sample_count} questions")
    if len(sys.argv) > 1:
        print(f"   PDF extraction: {pdf_count} questions")
    
    if sample_count >= 3:
        print("✅ Explicit question extraction is working!")
        print("🚀 Ready to process your 395-question PDF")
    else:
        print("❌ Explicit question extraction needs debugging")
        print("🔧 Check the regex patterns and parsing logic")

if __name__ == "__main__":
    main()
