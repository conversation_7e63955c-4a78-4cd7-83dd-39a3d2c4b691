import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

interface PdfGeneratorPayload {
  title: string;
  description: string;
  duration: number;
  totalMarks: number;
  questions: Array<{
    question: string;
    options: string[];
    answer: string;
    subject?: string;
  }>;
  includeAnswers: boolean;
  filename?: string;
  collegeName?: string;
  collegeLogoUrl?: string;
}

export const POST = async (req: NextRequest) => {
  try {
    const payload = (await req.json()) as PdfGeneratorPayload;

    const {
      title,
      description,
      duration,
      totalMarks,
      questions,
      includeAnswers,
      filename = 'question-paper.pdf',
      collegeName = '',
      collegeLogoUrl = '',
    } = payload;

    const html = `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${title}</title>
  <link href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.renderMathInElement) {
        window.renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false,
          errorColor: '#cc0000',
          strict: false
        });
      }
    });
  </script>
  <style>
    @page {
      size: A4;
      margin: 25mm 15mm 20mm 15mm;
      @top-center {
        content: "${title}";
        font-size: 10pt;
        font-weight: bold;
      }
    }
    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }
    h1,h2,h3 { margin: 0; padding: 0; }
    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }
    /* Watermark */
    body::before {
      content: 'MEDICOS';
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 96pt;
      font-weight: bold;
      color: rgba(0,128,0,0.08); /* greenish */
      z-index: 0;
      pointer-events: none;
    }
    /* Header / Footer */
    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .college { display: flex; align-items: center; gap: 6px; }
    .college img { height: 32px; width: auto; }
    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }
    .meta { text-align: right; font-size: 10pt; }
    .meta div { margin: 0; }

    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }
    .subject-section { page-break-before: always; }
    .subject-section:first-child { page-break-before: avoid; }
    .subject-content {
      column-count: 2;
      column-gap: 10mm;
      column-rule: 1px solid #ccc; /* Add middle line separator */
      column-rule-style: solid;
    }
    .question { break-inside: avoid; margin-bottom: 12px; }
    .options { margin-left: 16px; }
    .options p { margin: 2px 0; }
    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }
    .subject-heading {
      font-weight: bold;
      font-size: 12pt;
      margin: 16px 0 12px;
      text-align: left;
      border-bottom: 1px solid #333;
      padding-bottom: 4px;
    }
  </style>
</head>
<body>
  <header>
    <div class="college">
      ${collegeLogoUrl ? `<img src="${collegeLogoUrl}" alt="logo" />` : ''}
      <span>${collegeName}</span>
    </div>
    <div class="title">${title}</div>
    <div class="meta">
      <div>Total Marks: ${totalMarks}</div>
      <div>Duration: ${duration} mins</div>
    </div>
  </header>
  <hr />
  <p>${description}</p>
  <div class="questions">
    ${(() => {
      // Group questions by subject
      const groupedQuestions = questions.reduce((groups, question) => {
        const subject = question.subject || 'General';
        if (!groups[subject]) {
          groups[subject] = [];
        }
        groups[subject].push(question);
        return groups;
      }, {} as Record<string, typeof questions>);

      // Generate HTML for each subject group
      return Object.entries(groupedQuestions).map(([subject, subjectQuestions]) => {
        const subjectHtml = `
          <div class="subject-section">
            <div class="subject-heading">Subject: ${subject}</div>
            <hr style="margin: 8px 0; border: none; border-top: 1px solid #333;" />
            <div class="subject-content">
              ${subjectQuestions.map((q, questionIndex) => {
                // Fix LaTeX formatting issues more comprehensively
                const fixedQuestion = q.question
                  // Fix the main \ffrac issue with braces
                  .replace(/\\ffrac\{/g, '\\frac{') // Fix \ffrac{ to \frac{

                  // Fix complex patterns from your examples - exact matches first
                  .replace(/\\ffracωLR/g, '\\frac{ω}{LR}') // \ffracωLR -> \frac{ω}{LR}
                  .replace(/\\ffrac1ωCR/g, '\\frac{1}{ωCR}') // \ffrac1ωCR -> \frac{1}{ωCR}
                  .replace(/\\ffracLC\\ffrac1R/g, '\\frac{LC}{\\frac{1}{R}}') // \ffracLC\ffrac1R -> \frac{LC}{\frac{1}{R}}
                  .replace(/\\ffracRLC/g, '\\frac{R}{LC}') // \ffracRLC -> \frac{R}{LC}
                  .replace(/\\ffrac100πMHz/g, '\\frac{100}{πMHz}') // \ffrac100πMHz -> \frac{100}{πMHz}
                  .replace(/\\ffrac1000πHz/g, '\\frac{1000}{πHz}') // \ffrac1000πHz -> \frac{1000}{πHz}
                  .replace(/\\ffrac11000ohm/g, '\\frac{1}{1000ohm}') // \ffrac11000ohm -> \frac{1}{1000ohm}
                  .replace(/\\ffrac1Cω/g, '\\frac{1}{Cω}') // \ffrac1Cω -> \frac{1}{Cω}

                  // Fix general patterns with Greek letters and variables
                  .replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2}') // \ffracωLR -> \frac{ω}{LR}
                  .replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2$3}') // \ffrac1ωCR -> \frac{1}{ωCR}
                  .replace(/\\ffrac([A-Z]+)([A-Z]+)/g, '\\frac{$1}{$2}') // \ffracRLC -> \frac{R}{LC}

                  // Fix patterns that failed in tests
                  .replace(/\\ffrac(\d+)([πα-ωΩ])([A-Za-z]+)/g, '\\frac{$1}{$2$3}') // \ffrac100πMHz -> \frac{100}{πMHz}
                  .replace(/\\ffrac(\d+)(\d+)([a-z]+)/g, '\\frac{$1}{$2$3}') // \ffrac11000ohm -> \frac{1}{1000ohm}
                  .replace(/\\ffrac(\d+)([A-Z][a-z]*)/g, '\\frac{$1}{$2}') // \ffrac1Cω -> \frac{1}{Cω}

                  // Fix patterns without braces - more comprehensive
                  .replace(/\\ffrac([^{])/g, '\\frac$1') // Fix \ffrac without braces
                  .replace(/\\rac\{/g, '\\frac{') // Fix common LaTeX error
                  .replace(/\\fsqrt\{/g, '\\sqrt{') // Fix \fsqrt to \sqrt
                  .replace(/\\sqrt\{rac\{/g, '\\sqrt{\\frac{') // Fix sqrt with frac

                  // Fix function names with extra 'f'
                  .replace(/\\fsin\b/g, '\\sin') // Fix \fsin to \sin
                  .replace(/\\fcos\b/g, '\\cos') // Fix \fcos to \cos
                  .replace(/\\ftan\b/g, '\\tan') // Fix \ftan to \tan
                  .replace(/\\flog\b/g, '\\log') // Fix \flog to \log
                  .replace(/\\fln\b/g, '\\ln') // Fix \fln to \ln

                  // Fix function braces
                  .replace(/\\sin\{/g, '\\sin ') // Fix sin function
                  .replace(/\\cos\{/g, '\\cos ') // Fix cos function
                  .replace(/\\tan\{/g, '\\tan ') // Fix tan function
                  .replace(/\\log\{/g, '\\log ') // Fix log function
                  .replace(/\\ln\{/g, '\\ln ') // Fix ln function

                  // Fix content within $ delimiters
                  .replace(/\$([^$]*rac[^$]*)\$/g, (_, content) => {
                    return '$' + content.replace(/rac\{/g, 'frac{') + '$';
                  })

                  // Fix malformed fractions like \ffrac100πMHz
                  .replace(/\\ffrac(\d+)([πα-ωΩ])/g, '\\frac{$1}{$2}') // \ffrac100π -> \frac{100}{π}
                  .replace(/\\ffrac(\d+)(\w+)/g, '\\frac{$1}{$2}') // \ffrac1000Hz -> \frac{1000}{Hz}

                  // Clean up any remaining malformed LaTeX
                  .replace(/([a-zA-Z])\\ffrac/g, '$1 \\frac') // Fix cases like "V\ffrac"
                  .replace(/\\ffrac([A-Z])/g, '\\frac{}{$1}'); // Fix cases like "\ffracV"

                const fixedOptions = q.options.map(opt =>
                  opt
                    // Fix the main \ffrac issue with braces
                    .replace(/\\ffrac\{/g, '\\frac{') // Fix \ffrac{ to \frac{

                    // Fix complex patterns from your examples - exact matches first
                    .replace(/\\ffracωLR/g, '\\frac{ω}{LR}') // \ffracωLR -> \frac{ω}{LR}
                    .replace(/\\ffrac1ωCR/g, '\\frac{1}{ωCR}') // \ffrac1ωCR -> \frac{1}{ωCR}
                    .replace(/\\ffracLC\\ffrac1R/g, '\\frac{LC}{\\frac{1}{R}}') // \ffracLC\ffrac1R -> \frac{LC}{\frac{1}{R}}
                    .replace(/\\ffracRLC/g, '\\frac{R}{LC}') // \ffracRLC -> \frac{R}{LC}
                    .replace(/\\ffrac100πMHz/g, '\\frac{100}{πMHz}') // \ffrac100πMHz -> \frac{100}{πMHz}
                    .replace(/\\ffrac1000πHz/g, '\\frac{1000}{πHz}') // \ffrac1000πHz -> \frac{1000}{πHz}
                    .replace(/\\ffrac11000ohm/g, '\\frac{1}{1000ohm}') // \ffrac11000ohm -> \frac{1}{1000ohm}
                    .replace(/\\ffrac1Cω/g, '\\frac{1}{Cω}') // \ffrac1Cω -> \frac{1}{Cω}

                    // Fix general patterns with Greek letters and variables
                    .replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2}') // \ffracωLR -> \frac{ω}{LR}
                    .replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g, '\\frac{$1}{$2$3}') // \ffrac1ωCR -> \frac{1}{ωCR}
                    .replace(/\\ffrac([A-Z]+)([A-Z]+)/g, '\\frac{$1}{$2}') // \ffracRLC -> \frac{R}{LC}

                    // Fix patterns that failed in tests
                    .replace(/\\ffrac(\d+)([πα-ωΩ])([A-Za-z]+)/g, '\\frac{$1}{$2$3}') // \ffrac100πMHz -> \frac{100}{πMHz}
                    .replace(/\\ffrac(\d+)(\d+)([a-z]+)/g, '\\frac{$1}{$2$3}') // \ffrac11000ohm -> \frac{1}{1000ohm}
                    .replace(/\\ffrac(\d+)([A-Z][a-z]*)/g, '\\frac{$1}{$2}') // \ffrac1Cω -> \frac{1}{Cω}

                    // Fix patterns without braces - more comprehensive
                    .replace(/\\ffrac([^{])/g, '\\frac$1') // Fix \ffrac without braces
                    .replace(/\\rac\{/g, '\\frac{')
                    .replace(/\\fsqrt\{/g, '\\sqrt{') // Fix \fsqrt to \sqrt
                    .replace(/\\sqrt\{rac\{/g, '\\sqrt{\\frac{')

                    // Fix function names with extra 'f'
                    .replace(/\\fsin\b/g, '\\sin') // Fix \fsin to \sin
                    .replace(/\\fcos\b/g, '\\cos') // Fix \fcos to \cos
                    .replace(/\\ftan\b/g, '\\tan') // Fix \ftan to \tan
                    .replace(/\\flog\b/g, '\\log') // Fix \flog to \log
                    .replace(/\\fln\b/g, '\\ln') // Fix \fln to \ln

                    // Fix function braces
                    .replace(/\\sin\{/g, '\\sin ')
                    .replace(/\\cos\{/g, '\\cos ')
                    .replace(/\\tan\{/g, '\\tan ')
                    .replace(/\\log\{/g, '\\log ')
                    .replace(/\\ln\{/g, '\\ln ')

                    // Fix content within $ delimiters
                    .replace(/\$([^$]*rac[^$]*)\$/g, (_, content) => {
                      return '$' + content.replace(/rac\{/g, 'frac{') + '$';
                    })

                    // Fix malformed fractions in options
                    .replace(/\\ffrac(\d+)([πα-ωΩ])/g, '\\frac{$1}{$2}') // \ffrac100π -> \frac{100}{π}
                    .replace(/\\ffrac(\d+)(\w+)/g, '\\frac{$1}{$2}') // \ffrac1000Hz -> \frac{1000}{Hz}

                    // Clean up any remaining malformed LaTeX
                    .replace(/([a-zA-Z])\\ffrac/g, '$1 \\frac') // Fix cases like "V\ffrac"
                    .replace(/\\ffrac([A-Z])/g, '\\frac{}{$1}') // Fix cases like "\ffracV"
                );

                return `
                  <div class="question">
                    <p><strong>${questionIndex + 1}.</strong> ${fixedQuestion}</p>
                    <div class="options">
                      ${fixedOptions.map((opt, i) => `<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}
                      ${includeAnswers ? `<p><em>Answer:</em> ${q.answer}</p>` : ''}
                    </div>
                  </div>`;
              }).join('')}
            </div>
          </div>`;
        return subjectHtml;
      }).join('');
    })()}
  </div>
  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>
</body>
</html>`;

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'networkidle0' });

    // Wait for KaTeX to load and render math
    await page.waitForFunction(() => {
      return (window as any).renderMathInElement !== undefined;
    }, { timeout: 5000 }).catch(() => {});

    // Trigger math rendering manually if needed
    await page.evaluate(() => {
      if ((window as any).renderMathInElement) {
        (window as any).renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false,
          errorColor: '#cc0000',
          strict: false
        });
      }
    });

    // Wait for rendering to complete
    await page.waitForFunction(() => {
      const mathElements = document.querySelectorAll('script[type="math/tex"]');
      const katexElements = document.querySelectorAll('.katex');
      return mathElements.length === 0 || katexElements.length > 0;
    }, { timeout: 5000 }).catch(() => {});

    // Extra delay to ensure layout settles
    await new Promise(resolve => setTimeout(resolve, 500));

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20mm', right: '15mm', bottom: '20mm', left: '15mm' },
    });

    await browser.close();

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error: any) {
    console.error('PDF generation failed:', error);
    return new NextResponse(JSON.stringify({ error: 'PDF generation failed' }), { status: 500 });
  }
};
