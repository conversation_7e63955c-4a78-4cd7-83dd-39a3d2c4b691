/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-solutions-pdf/route";
exports.ids = ["app/api/generate-solutions-pdf/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-solutions-pdf/route.ts */ \"(rsc)/./src/app/api/generate-solutions-pdf/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-solutions-pdf/route\",\n        pathname: \"/api/generate-solutions-pdf\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-solutions-pdf/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-solutions-pdf\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_solutions_pdf_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-solutions-pdf/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/generate-solutions-pdf/route.ts ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_1__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        console.log('Solutions PDF API called with payload:', payload);\n        const { title, description, duration, totalMarks, questions, filename = 'question-paper-solutions.pdf', collegeName = '', collegeLogoUrl = '' } = payload;\n        const html = `<!doctype html>\n<html>\n<head>\n  <meta charset=\"utf-8\" />\n  <title>${title} - Solutions</title>\n  <link href=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css\" rel=\"stylesheet\" />\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js\"></script>\n  <script src=\"https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js\"></script>\n  <script>\n    document.addEventListener(\"DOMContentLoaded\", function() {\n      if (window.renderMathInElement) {\n        window.renderMathInElement(document.body, {\n          delimiters: [\n            {left: '$$', right: '$$', display: true},\n            {left: '$', right: '$', display: false},\n            {left: '\\\\(', right: '\\\\)', display: false},\n            {left: '\\\\[', right: '\\\\]', display: true}\n          ]\n        });\n      }\n    });\n  </script>\n  <style>\n    @page { size: A4; margin: 20mm 15mm; }\n    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }\n    h1,h2,h3 { margin: 0; padding: 0; }\n    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }\n    /* Watermark */\n    body::before {\n      content: 'MEDICOS';\n      position: fixed;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%) rotate(-30deg);\n      font-size: 96pt;\n      font-weight: bold;\n      color: rgba(0,128,0,0.08); /* greenish */\n      z-index: 0;\n      pointer-events: none;\n    }\n    /* Header / Footer */\n    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n    .college { display: flex; align-items: center; gap: 6px; }\n    .college img { height: 32px; width: auto; }\n    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }\n    .meta { text-align: right; font-size: 10pt; }\n    .meta div { margin: 0; }\n\n    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }\n    .question { break-inside: avoid; margin-bottom: 20px; page-break-inside: avoid; }\n    .question-content { margin-bottom: 8px; }\n    .options { margin-left: 12px; margin-bottom: 8px; }\n    .options p { margin: 2px 0; }\n    .answer { margin-top: 8px; padding: 6px 10px; background-color: #f0f8ff; border-left: 4px solid #2563eb; border-radius: 3px; }\n    .answer-text { font-weight: bold; color: #000; font-size: 10pt; }\n    .solution { margin-top: 10px; padding: 8px 12px; background-color: #f9f9f9; border-left: 4px solid #10b981; border-radius: 3px; }\n    .solution-title { font-weight: bold; color: #059669; margin-bottom: 6px; font-size: 10pt; }\n    .solution-content { font-size: 9pt; line-height: 1.4; }\n    .solution-content p { margin: 4px 0; }\n    .solution-content ol { margin: 4px 0; padding-left: 16px; }\n    .solution-content li { margin: 2px 0; }\n    .hints { margin-top: 10px; padding: 8px 12px; background-color: #fef3c7; border-left: 4px solid #f59e0b; border-radius: 3px; }\n    .hints-title { font-weight: bold; color: #d97706; margin-bottom: 6px; font-size: 10pt; }\n    .hint-item { margin: 3px 0; font-size: 9pt; line-height: 1.3; }\n    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }\n    .subject-heading { font-weight: bold; margin: 12px 0 8px; font-size: 12pt; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px; }\n  </style>\n</head>\n<body>\n  <header>\n    <div class=\"college\">\n      ${collegeLogoUrl ? `<img src=\"${collegeLogoUrl}\" alt=\"logo\" />` : ''}\n      <span>${collegeName}</span>\n    </div>\n    <div class=\"title\">${title} - Solutions</div>\n    <div class=\"meta\">\n      <div>Total Marks: ${totalMarks}</div>\n      <div>Duration: ${duration} mins</div>\n    </div>\n  </header>\n  <hr />\n  <p>${description}</p>\n  <div class=\"questions\">\n    ${questions.reduce((acc, q, idx)=>{\n            // Check if this is a new subject\n            const isNewSubject = q.subject && (idx === 0 || questions[idx - 1].subject !== q.subject);\n            // Calculate question number within current subject\n            let questionNumber = 1;\n            if (!isNewSubject) {\n                // Count questions in current subject up to this point\n                const currentSubject = q.subject;\n                for(let i = idx - 1; i >= 0; i--){\n                    if (questions[i].subject === currentSubject) {\n                        questionNumber++;\n                    } else {\n                        break;\n                    }\n                }\n            }\n            const heading = isNewSubject ? `<div class=\"subject-heading\">Subject: ${q.subject}</div>` : '';\n            // Find the correct option letter for the answer\n            const answerIndex = q.options.findIndex((opt)=>opt === q.answer);\n            const answerLetter = answerIndex !== -1 ? String.fromCharCode(97 + answerIndex) : q.answer;\n            // Build solution section\n            let solutionHtml = '';\n            if (q.solution) {\n                const solutionParts = [];\n                if (q.solution.final_explanation) {\n                    solutionParts.push(`<p><strong>Explanation:</strong> ${q.solution.final_explanation}</p>`);\n                }\n                if (q.solution.methodology) {\n                    solutionParts.push(`<p><strong>Method:</strong> ${q.solution.methodology}</p>`);\n                }\n                if (q.solution.key_concepts && q.solution.key_concepts.length > 0) {\n                    solutionParts.push(`<p><strong>Key Concepts:</strong> ${q.solution.key_concepts.join(', ')}</p>`);\n                }\n                if (q.solution.steps && q.solution.steps.length > 0) {\n                    solutionParts.push(`<p><strong>Steps:</strong></p><ol>${q.solution.steps.map((step)=>`<li>${step}</li>`).join('')}</ol>`);\n                }\n                if (solutionParts.length > 0) {\n                    solutionHtml = `\n            <div class=\"solution\">\n              <div class=\"solution-title\">Solution:</div>\n              <div class=\"solution-content\">\n                ${solutionParts.join('')}\n              </div>\n            </div>`;\n                }\n            }\n            // Build hints section\n            let hintsHtml = '';\n            if (q.hints && q.hints.length > 0) {\n                hintsHtml = `\n          <div class=\"hints\">\n            <div class=\"hints-title\">Hints:</div>\n            ${q.hints.map((hint, i)=>`<div class=\"hint-item\">${i + 1}. ${hint}</div>`).join('')}\n          </div>`;\n            }\n            const qHtml = `\n        <div class=\"question\">\n          <div class=\"question-content\">\n            <p><strong>${questionNumber}.</strong> ${q.question}</p>\n          </div>\n          <div class=\"options\">\n            ${q.options.map((opt, i)=>`<p>${String.fromCharCode(97 + i)}) ${opt}</p>`).join('')}\n          </div>\n          <div class=\"answer\">\n            <p class=\"answer-text\">Answer: ${answerLetter})</p>\n          </div>\n          ${solutionHtml}\n          ${hintsHtml}\n        </div>`;\n            return acc + heading + qHtml;\n        }, '')}\n  </div>\n  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>\n</body>\n</html>`;\n        console.log('Launching Puppeteer for solutions PDF generation...');\n        const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_1__[\"default\"].launch({\n            args: [\n                '--no-sandbox',\n                '--disable-setuid-sandbox'\n            ]\n        });\n        const page = await browser.newPage();\n        console.log('Setting HTML content for solutions PDF...');\n        await page.setContent(html, {\n            waitUntil: 'networkidle0'\n        });\n        // Wait until KaTeX has rendered math. Small delay to be safe.\n        await page.waitForFunction(()=>{\n            return Array.from(document.querySelectorAll('.katex')).length > 0;\n        }, {\n            timeout: 3000\n        }).catch(()=>{});\n        // Extra small delay to ensure layout settles\n        await new Promise((resolve)=>setTimeout(resolve, 200));\n        console.log('Generating solutions PDF...');\n        const pdfBuffer = await page.pdf({\n            format: 'A4',\n            printBackground: true,\n            margin: {\n                top: '20mm',\n                right: '15mm',\n                bottom: '20mm',\n                left: '15mm'\n            }\n        });\n        await browser.close();\n        console.log('Solutions PDF generated successfully, size:', pdfBuffer.length, 'bytes');\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(pdfBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/pdf',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('Solutions PDF generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'Solutions PDF generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-solutions-pdf/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-solutions-pdf%2Froute&page=%2Fapi%2Fgenerate-solutions-pdf%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-solutions-pdf%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();