/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-answers-excel/route";
exports.ids = ["app/api/generate-answers-excel/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-answers-excel%2Froute&page=%2Fapi%2Fgenerate-answers-excel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-answers-excel%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-answers-excel%2Froute&page=%2Fapi%2Fgenerate-answers-excel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-answers-excel%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_answers_excel_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-answers-excel/route.ts */ \"(rsc)/./src/app/api/generate-answers-excel/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-answers-excel/route\",\n        pathname: \"/api/generate-answers-excel\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-answers-excel/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\api\\\\generate-answers-excel\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_adars_Desktop_FL_medicos_medicos_frontend_src_app_api_generate_answers_excel_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-answers-excel%2Froute&page=%2Fapi%2Fgenerate-answers-excel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-answers-excel%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-answers-excel/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/generate-answers-excel/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! xlsx */ \"(rsc)/./node_modules/xlsx/xlsx.mjs\");\n\n\nconst POST = async (req)=>{\n    try {\n        const payload = await req.json();\n        const { title, questions, filename = 'question-paper-answers.xlsx' } = payload;\n        const worksheetData = [];\n        // Add title row\n        worksheetData.push([\n            title || 'Question Paper - Answer Key'\n        ]);\n        worksheetData.push([]); // Empty row for spacing\n        // Add header row\n        worksheetData.push([\n            'Subject',\n            'Question No.',\n            'Correct Answer'\n        ]);\n        // Process questions by subject\n        let currentSubject = '';\n        let questionNumberInSubject = 1;\n        questions.forEach((q, idx)=>{\n            // Check if this is a new subject\n            const isNewSubject = q.subject && q.subject !== currentSubject;\n            if (isNewSubject) {\n                currentSubject = q.subject || '';\n                questionNumberInSubject = 1;\n            } else if (!isNewSubject && idx > 0) {\n                questionNumberInSubject++;\n            }\n            // Find the correct option letter for the answer\n            const answerIndex = q.options.findIndex((opt)=>opt === q.answer);\n            const answerLetter = answerIndex !== -1 ? String.fromCharCode(97 + answerIndex) : q.answer;\n            // Add row to worksheet\n            worksheetData.push([\n                q.subject || 'General',\n                questionNumberInSubject,\n                `${answerLetter})`\n            ]);\n        });\n        // Create workbook and worksheet\n        const workbook = xlsx__WEBPACK_IMPORTED_MODULE_1__.utils.book_new();\n        const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_1__.utils.aoa_to_sheet(worksheetData);\n        // Set column widths\n        worksheet['!cols'] = [\n            {\n                width: 20\n            },\n            {\n                width: 15\n            },\n            {\n                width: 20\n            } // Answer\n        ];\n        // Style the title row\n        if (worksheet['A1']) {\n            worksheet['A1'].s = {\n                font: {\n                    bold: true,\n                    sz: 14\n                },\n                alignment: {\n                    horizontal: 'center'\n                }\n            };\n        }\n        // Style the header row\n        const headerRowIndex = 3; // Row 3 (0-indexed: row 2)\n        [\n            'A',\n            'B',\n            'C'\n        ].forEach((col, index)=>{\n            const cellRef = `${col}${headerRowIndex}`;\n            if (worksheet[cellRef]) {\n                worksheet[cellRef].s = {\n                    font: {\n                        bold: true\n                    },\n                    fill: {\n                        fgColor: {\n                            rgb: 'E3F2FD'\n                        }\n                    },\n                    alignment: {\n                        horizontal: 'center'\n                    }\n                };\n            }\n        });\n        // Merge title cell across columns\n        worksheet['!merges'] = [\n            {\n                s: {\n                    r: 0,\n                    c: 0\n                },\n                e: {\n                    r: 0,\n                    c: 2\n                }\n            }\n        ];\n        // Add borders to data rows\n        const dataStartRow = 4; // Row 4 (0-indexed: row 3)\n        for(let row = dataStartRow; row <= worksheetData.length; row++){\n            [\n                'A',\n                'B',\n                'C'\n            ].forEach((col)=>{\n                const cellRef = `${col}${row}`;\n                if (worksheet[cellRef]) {\n                    worksheet[cellRef].s = {\n                        ...worksheet[cellRef].s,\n                        border: {\n                            top: {\n                                style: 'thin'\n                            },\n                            bottom: {\n                                style: 'thin'\n                            },\n                            left: {\n                                style: 'thin'\n                            },\n                            right: {\n                                style: 'thin'\n                            }\n                        },\n                        alignment: {\n                            horizontal: 'center'\n                        }\n                    };\n                }\n            });\n        }\n        xlsx__WEBPACK_IMPORTED_MODULE_1__.utils.book_append_sheet(workbook, worksheet, 'Answer Key');\n        // Generate Excel buffer\n        const excelBuffer = xlsx__WEBPACK_IMPORTED_MODULE_1__.write(workbook, {\n            type: 'buffer',\n            bookType: 'xlsx',\n            compression: true\n        });\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(excelBuffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                'Content-Disposition': `attachment; filename=\"${filename}\"`\n            }\n        });\n    } catch (error) {\n        console.error('Answers Excel generation failed:', error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify({\n            error: 'Answers Excel generation failed'\n        }), {\n            status: 500\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-answers-excel/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/xlsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-answers-excel%2Froute&page=%2Fapi%2Fgenerate-answers-excel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-answers-excel%2Froute.ts&appDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cadars%5CDesktop%5CFL%5Cmedicos%5Cmedicos-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();